<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.SysOrderMapper">

    <!-- 批量插入订单 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sys_order (
            payid, parents_payid, agent, anchor, amount_self, amount_fans,
            fee_rate, fee_value, fee_actual, fee_person, fee_account,
            order_status, settlement_status, source_order_id, vim_user_id,
            settlement_start_time, settlement_end_time, create_time, update_time
        ) VALUES
        <foreach collection="orders" item="order" separator=",">
            (
                #{order.payid}, #{order.parentsPayid}, #{order.agent}, #{order.anchor},
                #{order.amountSelf}, #{order.amountFans}, #{order.feeRate}, #{order.feeValue},
                #{order.feeActual}, #{order.feePerson}, #{order.feeAccount},
                #{order.orderStatus}, #{order.settlementStatus}, #{order.sourceOrderId},
                #{order.vimUserId}, #{order.settlementStartTime}, #{order.settlementEndTime},
                UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
            )
        </foreach>
    </insert>

    <!-- 查询主订单及其子订单（树形结构） -->
    <select id="selectMainOrdersWithSubOrders" resultType="com.jcloud.common.entity.SysOrder">
        SELECT 
            o1.*,
            o2.payid as sub_payid,
            o2.agent as sub_agent,
            o2.anchor as sub_anchor,
            o2.amount_self as sub_amount_self,
            o2.amount_fans as sub_amount_fans,
            o2.fee_rate as sub_fee_rate,
            o2.fee_value as sub_fee_value,
            o2.fee_actual as sub_fee_actual,
            o2.fee_person as sub_fee_person,
            o2.fee_account as sub_fee_account,
            o2.order_status as sub_order_status,
            o2.settlement_status as sub_settlement_status
        FROM sys_order o1
        LEFT JOIN sys_order o2 ON o1.payid = o2.parents_payid
        WHERE o1.parents_payid IS NULL
        ${ew.customSqlSegment}
        ORDER BY o1.create_time DESC, o2.create_time ASC
    </select>

    <!-- 批量更新订单状态 -->
    <update id="updateStatusBatch">
        UPDATE sys_order
        SET
            order_status = #{orderStatus},
            settlement_status = #{settlementStatus},
            update_time = UNIX_TIMESTAMP()
        WHERE payid IN
        <foreach collection="payids" item="payid" open="(" separator="," close=")">
            #{payid}
        </foreach>
    </update>

    <!-- 更新订单的实际结算金额和状态 -->
    <update id="updateOrderWithActualAmount">
        UPDATE sys_order
        SET
            actual_settlement_amount = #{actualSettlementAmount},
            order_status = #{orderStatus},
            settlement_status = #{settlementStatus},
            update_time = UNIX_TIMESTAMP()
        WHERE payid = #{payid}
    </update>

    <!-- 根据父订单ID查找所有子订单ID -->
    <select id="findSubOrderIdsByParentPayid" resultType="java.lang.String">
        SELECT payid
        FROM sys_order
        WHERE parents_payid = #{parentPayid}
    </select>



    <!-- 根据代理ID查询其所有订单（包括下级主播的订单） -->
    <select id="selectOrdersByAgent" resultType="com.jcloud.common.entity.SysOrder">
        SELECT * FROM sys_order 
        WHERE (agent = #{agentId} OR anchor IN (
            SELECT CAST(id AS CHAR) FROM vim_user 
            WHERE invite_user = CAST(#{agentId} AS UNSIGNED) AND identity IN (2, 3)
        ))
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据主播ID查询其订单 -->
    <select id="selectOrdersByAnchor" resultType="com.jcloud.common.entity.SysOrder">
        SELECT * FROM sys_order 
        WHERE anchor = #{anchorId}
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 统计指定时间范围内的订单数量 -->
    <select id="countOrdersByTimeRange" resultType="java.lang.Long">
        SELECT COUNT(*) FROM sys_order 
        WHERE create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}
        <if test="orderStatus != null">
            AND order_status = #{orderStatus}
        </if>
    </select>

    <!-- 查询指定用户在指定结算时间范围内是否已有订单 -->
    <select id="existsOrderByUserAndTimeRange" resultType="boolean">
        SELECT EXISTS (
            SELECT 1 FROM sys_order
            WHERE (agent = #{userId} OR anchor = #{userId})
            AND settlement_start_time &lt;= #{endTime}
            AND settlement_end_time &gt;= #{startTime}
            AND order_status != 4
        )
    </select>

</mapper>
