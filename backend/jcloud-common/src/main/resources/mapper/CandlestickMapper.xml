<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.common.mapper.CandlestickMapper">

    <!--
    性能优化建议：
    1. 为vim_order_recharge表添加复合索引：(state, create_time)
    2. 为sys_order表添加复合索引：(create_time, fee_value)
    3. 考虑为大数据量场景添加分区表
    -->

    <!-- 查询收入K线数据 -->
    <select id="selectRevenueKlineData" resultType="com.jcloud.common.dto.CandlestickResponse">
        SELECT 
            <choose>
                <when test="timeframe == 'day'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d') as date
                </when>
                <when test="timeframe == 'week'">
                    CONCAT(YEAR(FROM_UNIXTIME(create_time)), '-W', LPAD(WEEK(FROM_UNIXTIME(create_time), 1), 2, '0')) as date
                </when>
                <when test="timeframe == 'month'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m') as date
                </when>
                <otherwise>
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d') as date
                </otherwise>
            </choose>,
            MIN(amount) as low,
            MAX(amount) as high,
            SUBSTRING_INDEX(GROUP_CONCAT(amount ORDER BY create_time), ',', 1) as open,
            SUBSTRING_INDEX(GROUP_CONCAT(amount ORDER BY create_time DESC), ',', 1) as close,
            COUNT(*) as volume,
            SUM(amount) as amount
        FROM vim_order_recharge
        WHERE state = 2
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
        GROUP BY 
            <choose>
                <when test="timeframe == 'day'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </when>
                <when test="timeframe == 'week'">
                    YEAR(FROM_UNIXTIME(create_time)), WEEK(FROM_UNIXTIME(create_time), 1)
                </when>
                <when test="timeframe == 'month'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m')
                </when>
                <otherwise>
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </otherwise>
            </choose>
        ORDER BY date
    </select>

    <!-- 查询佣金K线数据 -->
    <select id="selectCommissionKlineData" resultType="com.jcloud.common.dto.CandlestickResponse">
        SELECT 
            <choose>
                <when test="timeframe == 'day'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d') as date
                </when>
                <when test="timeframe == 'week'">
                    CONCAT(YEAR(FROM_UNIXTIME(create_time)), '-W', LPAD(WEEK(FROM_UNIXTIME(create_time), 1), 2, '0')) as date
                </when>
                <when test="timeframe == 'month'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m') as date
                </when>
                <otherwise>
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d') as date
                </otherwise>
            </choose>,
            MIN(fee_value) as low,
            MAX(fee_value) as high,
            SUBSTRING_INDEX(GROUP_CONCAT(fee_value ORDER BY create_time), ',', 1) as open,
            SUBSTRING_INDEX(GROUP_CONCAT(fee_value ORDER BY create_time DESC), ',', 1) as close,
            COUNT(*) as volume,
            SUM(fee_value) as amount
        FROM sys_order 
        WHERE create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
        AND fee_value IS NOT NULL
        AND fee_value &gt; 0
        GROUP BY 
            <choose>
                <when test="timeframe == 'day'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </when>
                <when test="timeframe == 'week'">
                    YEAR(FROM_UNIXTIME(create_time)), WEEK(FROM_UNIXTIME(create_time), 1)
                </when>
                <when test="timeframe == 'month'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m')
                </when>
                <otherwise>
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </otherwise>
            </choose>
        ORDER BY date
    </select>

    <!-- 查询利润K线数据（跨库查询） -->
    <select id="selectProfitKlineData" resultType="com.jcloud.common.dto.CandlestickResponse">
        SELECT 
            date,
            MIN(profit) as low,
            MAX(profit) as high,
            SUBSTRING_INDEX(GROUP_CONCAT(profit ORDER BY create_time), ',', 1) as open,
            SUBSTRING_INDEX(GROUP_CONCAT(profit ORDER BY create_time DESC), ',', 1) as close,
            COUNT(*) as volume,
            SUM(profit) as amount
        FROM (
            SELECT 
                <choose>
                    <when test="timeframe == 'day'">
                        DATE_FORMAT(FROM_UNIXTIME(r.create_time), '%Y-%m-%d') as date
                    </when>
                    <when test="timeframe == 'week'">
                        CONCAT(YEAR(FROM_UNIXTIME(r.create_time)), '-W', LPAD(WEEK(FROM_UNIXTIME(r.create_time), 1), 2, '0')) as date
                    </when>
                    <when test="timeframe == 'month'">
                        DATE_FORMAT(FROM_UNIXTIME(r.create_time), '%Y-%m') as date
                    </when>
                    <otherwise>
                        DATE_FORMAT(FROM_UNIXTIME(r.create_time), '%Y-%m-%d') as date
                    </otherwise>
                </choose>,
                r.create_time,
                (r.amount - COALESCE(o.fee_value, 0)) as profit
            FROM vimbox.vim_order_recharge r
            LEFT JOIN cs2_skin_platform.sys_order o ON r.id = o.source_order_id
            WHERE r.state = 2
            AND r.create_time &gt;= #{startTime}
            AND r.create_time &lt;= #{endTime}
        ) profit_data
        GROUP BY date
        ORDER BY date
    </select>

    <!-- 查询收盘价数据用于技术指标计算 -->
    <select id="selectClosePricesForIndicators" resultType="java.math.BigDecimal">
        SELECT 
            SUBSTRING_INDEX(GROUP_CONCAT(amount ORDER BY create_time DESC), ',', 1) as close_price
        FROM vim_order_recharge
        WHERE state = 2
        AND create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}
        GROUP BY 
            <choose>
                <when test="timeframe == 'day'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </when>
                <when test="timeframe == 'week'">
                    YEAR(FROM_UNIXTIME(create_time)), WEEK(FROM_UNIXTIME(create_time), 1)
                </when>
                <when test="timeframe == 'month'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m')
                </when>
                <otherwise>
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </otherwise>
            </choose>
        ORDER BY 
            <choose>
                <when test="timeframe == 'day'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </when>
                <when test="timeframe == 'week'">
                    YEAR(FROM_UNIXTIME(create_time)), WEEK(FROM_UNIXTIME(create_time), 1)
                </when>
                <when test="timeframe == 'month'">
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m')
                </when>
                <otherwise>
                    DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m-%d')
                </otherwise>
            </choose>
    </select>

    <!-- 查询K线统计信息 -->
    <select id="selectKlineStatistics" resultType="java.util.Map">
        <choose>
            <when test="dataType == 'revenue'">
                SELECT 
                    COUNT(*) as total_volume,
                    AVG(amount) as avg_volume,
                    MAX(amount) as max_high,
                    MIN(amount) as min_low,
                    SUM(amount) as total_amount,
                    COUNT(DISTINCT uid) as unique_users
                FROM vim_order_recharge
                WHERE state = 2
                AND create_time &gt;= #{startTime}
                AND create_time &lt;= #{endTime}
            </when>
            <when test="dataType == 'commission'">
                SELECT 
                    COUNT(*) as total_volume,
                    AVG(fee_value) as avg_volume,
                    MAX(fee_value) as max_high,
                    MIN(fee_value) as min_low,
                    SUM(fee_value) as total_amount,
                    COUNT(DISTINCT user_id) as unique_users
                FROM sys_order 
                WHERE create_time &gt;= #{startTime}
                AND create_time &lt;= #{endTime}
                AND fee_value IS NOT NULL
                AND fee_value &gt; 0
            </when>
            <otherwise>
                SELECT 
                    0 as total_volume,
                    0 as avg_volume,
                    0 as max_high,
                    0 as min_low,
                    0 as total_amount,
                    0 as unique_users
            </otherwise>
        </choose>
    </select>

</mapper>
