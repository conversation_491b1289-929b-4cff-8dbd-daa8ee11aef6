package com.jcloud.common.mapper;

import com.jcloud.common.dto.CandlestickResponse;
import com.mybatisflex.annotation.UseDataSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * K线图数据Mapper接口
 * 提供K线图相关的数据查询功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface CandlestickMapper {
    
    /**
     * 查询收入K线数据（基于充值订单）
     * 使用从库数据源查询vim_order_recharge表
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param timeframe 时间粒度（day/week/month）
     * @return K线数据列表
     */
    @UseDataSource("slave")
    List<CandlestickResponse> selectRevenueKlineData(
        @Param("startTime") Integer startTime,
        @Param("endTime") Integer endTime,
        @Param("timeframe") String timeframe
    );
    
    /**
     * 查询佣金K线数据（基于佣金结算订单）
     * 使用主库数据源查询sys_order表
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param timeframe 时间粒度（day/week/month）
     * @return K线数据列表
     */
    @UseDataSource("master")
    List<CandlestickResponse> selectCommissionKlineData(
        @Param("startTime") Integer startTime,
        @Param("endTime") Integer endTime,
        @Param("timeframe") String timeframe
    );
    
    /**
     * 查询利润K线数据（收入减去佣金）
     * 需要跨库查询，使用主库数据源
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param timeframe 时间粒度（day/week/month）
     * @return K线数据列表
     */
    @UseDataSource("master")
    List<CandlestickResponse> selectProfitKlineData(
        @Param("startTime") Integer startTime,
        @Param("endTime") Integer endTime,
        @Param("timeframe") String timeframe
    );
    
    /**
     * 查询指定时间范围内的收入统计数据
     * 用于技术指标计算的基础数据
     * 
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 收盘价列表
     */
    @UseDataSource("slave")
    List<java.math.BigDecimal> selectClosePricesForIndicators(
        @Param("startTime") Integer startTime,
        @Param("endTime") Integer endTime,
        @Param("timeframe") String timeframe
    );
    
    /**
     * 查询K线数据的基础统计信息
     *
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param dataType 数据类型（revenue/profit/commission）
     * @return 统计信息Map
     */
    @UseDataSource("slave")
    java.util.Map<String, Object> selectKlineStatistics(
        @Param("startTime") Integer startTime,
        @Param("endTime") Integer endTime,
        @Param("dataType") String dataType
    );
}
