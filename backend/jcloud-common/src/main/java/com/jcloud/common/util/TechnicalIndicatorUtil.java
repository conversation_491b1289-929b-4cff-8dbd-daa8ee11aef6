package com.jcloud.common.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 技术指标计算工具类
 * 提供各种技术指标的计算方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TechnicalIndicatorUtil {
    
    /**
     * 计算简单移动平均线 (SMA)
     * 
     * @param data 数据列表
     * @param period 周期
     * @return SMA数据列表
     */
    public static List<BigDecimal> calculateSMA(List<BigDecimal> data, int period) {
        List<BigDecimal> result = new ArrayList<>();
        
        if (data == null || data.isEmpty() || period <= 0) {
            return result;
        }
        
        for (int i = 0; i < data.size(); i++) {
            if (i < period - 1) {
                result.add(null);
            } else {
                BigDecimal sum = BigDecimal.ZERO;
                for (int j = i - period + 1; j <= i; j++) {
                    if (data.get(j) != null) {
                        sum = sum.add(data.get(j));
                    }
                }
                result.add(sum.divide(new BigDecimal(period), 4, RoundingMode.HALF_UP));
            }
        }
        
        return result;
    }
    
    /**
     * 计算指数移动平均线 (EMA)
     * 
     * @param data 数据列表
     * @param period 周期
     * @return EMA数据列表
     */
    public static List<BigDecimal> calculateEMA(List<BigDecimal> data, int period) {
        List<BigDecimal> result = new ArrayList<>();
        
        if (data == null || data.isEmpty() || period <= 0) {
            return result;
        }
        
        BigDecimal multiplier = new BigDecimal(2).divide(new BigDecimal(period + 1), 8, RoundingMode.HALF_UP);
        
        // 第一个值使用SMA
        BigDecimal ema = null;
        for (int i = 0; i < data.size(); i++) {
            if (i < period - 1) {
                result.add(null);
            } else if (i == period - 1) {
                // 计算初始SMA
                BigDecimal sum = BigDecimal.ZERO;
                for (int j = 0; j < period; j++) {
                    if (data.get(j) != null) {
                        sum = sum.add(data.get(j));
                    }
                }
                ema = sum.divide(new BigDecimal(period), 4, RoundingMode.HALF_UP);
                result.add(ema);
            } else {
                // 计算EMA
                if (data.get(i) != null && ema != null) {
                    ema = data.get(i).subtract(ema).multiply(multiplier).add(ema);
                    result.add(ema);
                } else {
                    result.add(null);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 计算布林带
     * 
     * @param data 数据列表
     * @param period 周期
     * @param stdDev 标准差倍数
     * @return 布林带数据（包含上轨、中轨、下轨）
     */
    public static Map<String, List<BigDecimal>> calculateBollingerBands(
            List<BigDecimal> data, int period, int stdDev) {
        Map<String, List<BigDecimal>> result = new HashMap<>();
        
        List<BigDecimal> sma = calculateSMA(data, period);
        List<BigDecimal> upper = new ArrayList<>();
        List<BigDecimal> lower = new ArrayList<>();
        
        for (int i = 0; i < data.size(); i++) {
            if (i < period - 1 || sma.get(i) == null) {
                upper.add(null);
                lower.add(null);
            } else {
                BigDecimal mean = sma.get(i);
                
                // 计算标准差
                BigDecimal variance = BigDecimal.ZERO;
                int validCount = 0;
                for (int j = i - period + 1; j <= i; j++) {
                    if (data.get(j) != null) {
                        BigDecimal diff = data.get(j).subtract(mean);
                        variance = variance.add(diff.multiply(diff));
                        validCount++;
                    }
                }
                
                if (validCount > 0) {
                    variance = variance.divide(new BigDecimal(validCount), 8, RoundingMode.HALF_UP);
                    BigDecimal standardDeviation = BigDecimal.valueOf(Math.sqrt(variance.doubleValue()));
                    BigDecimal deviation = standardDeviation.multiply(new BigDecimal(stdDev));
                    
                    upper.add(mean.add(deviation));
                    lower.add(mean.subtract(deviation));
                } else {
                    upper.add(null);
                    lower.add(null);
                }
            }
        }
        
        result.put("BOLL_UPPER", upper);
        result.put("BOLL_MIDDLE", sma);
        result.put("BOLL_LOWER", lower);
        
        return result;
    }
    
    /**
     * 计算RSI相对强弱指数
     * 
     * @param data 数据列表
     * @param period 周期
     * @return RSI数据列表
     */
    public static List<BigDecimal> calculateRSI(List<BigDecimal> data, int period) {
        List<BigDecimal> result = new ArrayList<>();
        
        if (data == null || data.size() < 2 || period <= 0) {
            return result;
        }
        
        List<BigDecimal> gains = new ArrayList<>();
        List<BigDecimal> losses = new ArrayList<>();
        
        // 计算价格变化
        for (int i = 1; i < data.size(); i++) {
            if (data.get(i) != null && data.get(i - 1) != null) {
                BigDecimal change = data.get(i).subtract(data.get(i - 1));
                gains.add(change.compareTo(BigDecimal.ZERO) > 0 ? change : BigDecimal.ZERO);
                losses.add(change.compareTo(BigDecimal.ZERO) < 0 ? change.abs() : BigDecimal.ZERO);
            } else {
                gains.add(BigDecimal.ZERO);
                losses.add(BigDecimal.ZERO);
            }
        }
        
        result.add(null); // 第一个值无法计算
        
        for (int i = 0; i < gains.size(); i++) {
            if (i < period - 1) {
                result.add(null);
            } else {
                BigDecimal avgGain = BigDecimal.ZERO;
                BigDecimal avgLoss = BigDecimal.ZERO;
                
                for (int j = i - period + 1; j <= i; j++) {
                    avgGain = avgGain.add(gains.get(j));
                    avgLoss = avgLoss.add(losses.get(j));
                }
                
                avgGain = avgGain.divide(new BigDecimal(period), 4, RoundingMode.HALF_UP);
                avgLoss = avgLoss.divide(new BigDecimal(period), 4, RoundingMode.HALF_UP);
                
                if (avgLoss.compareTo(BigDecimal.ZERO) == 0) {
                    result.add(new BigDecimal("100"));
                } else {
                    BigDecimal rs = avgGain.divide(avgLoss, 4, RoundingMode.HALF_UP);
                    BigDecimal rsi = new BigDecimal("100").subtract(
                        new BigDecimal("100").divide(BigDecimal.ONE.add(rs), 4, RoundingMode.HALF_UP));
                    result.add(rsi);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 计算所有技术指标
     * 
     * @param closePrices 收盘价列表
     * @param indicators 要计算的指标列表
     * @return 技术指标数据Map
     */
    public static Map<String, List<BigDecimal>> calculateAllIndicators(
            List<BigDecimal> closePrices, List<String> indicators) {
        Map<String, List<BigDecimal>> result = new HashMap<>();
        
        if (closePrices == null || closePrices.isEmpty()) {
            return result;
        }
        
        if (indicators == null || indicators.isEmpty()) {
            indicators = List.of("MA5", "MA20", "BOLL", "RSI");
        }
        
        for (String indicator : indicators) {
            try {
                switch (indicator) {
                    case "MA5":
                        result.put("MA5", calculateSMA(closePrices, 5));
                        break;
                    case "MA10":
                        result.put("MA10", calculateSMA(closePrices, 10));
                        break;
                    case "MA20":
                        result.put("MA20", calculateSMA(closePrices, 20));
                        break;
                    case "MA60":
                        result.put("MA60", calculateSMA(closePrices, 60));
                        break;
                    case "EMA12":
                        result.put("EMA12", calculateEMA(closePrices, 12));
                        break;
                    case "EMA26":
                        result.put("EMA26", calculateEMA(closePrices, 26));
                        break;
                    case "BOLL":
                        Map<String, List<BigDecimal>> boll = calculateBollingerBands(closePrices, 20, 2);
                        result.putAll(boll);
                        break;
                    case "RSI":
                        result.put("RSI", calculateRSI(closePrices, 14));
                        break;
                    default:
                        log.warn("不支持的技术指标: {}", indicator);
                        break;
                }
            } catch (Exception e) {
                log.error("计算技术指标 {} 时发生异常", indicator, e);
            }
        }
        
        return result;
    }
}
