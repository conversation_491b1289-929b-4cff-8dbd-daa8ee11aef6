package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * K线图数据查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "K线图数据查询请求")
public class CandlestickRequest {
    
    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-06-01 00:00:00")
    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "开始时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式")
    private String startTime;
    
    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-08-31 23:59:59")
    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "结束时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式")
    private String endTime;
    
    /**
     * 时间粒度（day/week/month）
     */
    @Schema(description = "时间粒度", example = "day", allowableValues = {"day", "week", "month"})
    @NotBlank(message = "时间粒度不能为空")
    @Pattern(regexp = "^(day|week|month)$", message = "时间粒度只能是 day、week 或 month")
    private String timeframe;
    
    /**
     * 数据类型（revenue/profit/commission）
     */
    @Schema(description = "数据类型", example = "revenue", allowableValues = {"revenue", "profit", "commission"})
    @NotBlank(message = "数据类型不能为空")
    @Pattern(regexp = "^(revenue|profit|commission)$", message = "数据类型只能是 revenue、profit 或 commission")
    private String dataType;
    
    /**
     * 是否包含技术指标
     */
    @Schema(description = "是否包含技术指标", example = "true")
    private Boolean includeTechnicalIndicators = false;
    
    /**
     * 技术指标列表
     */
    @Schema(description = "技术指标列表", example = "[\"MA5\", \"MA20\", \"BOLL\", \"RSI\"]")
    private java.util.List<String> technicalIndicators;
    
    /**
     * 最大数据点数（用于数据降采样）
     */
    @Schema(description = "最大数据点数", example = "1000")
    private Integer maxDataPoints = 1000;
}
