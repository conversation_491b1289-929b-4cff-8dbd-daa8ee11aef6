package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;

/**
 * K线图数据响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "K线图数据响应")
public class CandlestickResponse {
    
    /**
     * 日期
     */
    @Schema(description = "日期", example = "2025-08-01")
    private String date;
    
    /**
     * 开盘价
     */
    @Schema(description = "开盘价", example = "1000.50")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal open;
    
    /**
     * 收盘价
     */
    @Schema(description = "收盘价", example = "1050.75")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal close;
    
    /**
     * 最低价
     */
    @Schema(description = "最低价", example = "980.25")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal low;
    
    /**
     * 最高价
     */
    @Schema(description = "最高价", example = "1080.00")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal high;
    
    /**
     * 成交量
     */
    @Schema(description = "成交量", example = "150")
    private Long volume;
    
    /**
     * 成交额
     */
    @Schema(description = "成交额", example = "157575.00")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal amount;
    
    /**
     * 涨跌额
     */
    @Schema(description = "涨跌额", example = "50.25")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal change;
    
    /**
     * 涨跌幅
     */
    @Schema(description = "涨跌幅", example = "5.02")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal changePercent;
    
    /**
     * 振幅
     */
    @Schema(description = "振幅", example = "10.00")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal amplitude;
    
    /**
     * 换手率（如果适用）
     */
    @Schema(description = "换手率", example = "2.50")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal turnoverRate;
}
