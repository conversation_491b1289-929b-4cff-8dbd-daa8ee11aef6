package com.jcloud.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * K线图完整数据响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "K线图完整数据响应")
public class CandlestickDataResponse {
    
    /**
     * K线数据
     */
    @Schema(description = "K线数据")
    private List<CandlestickResponse> candleData;
    
    /**
     * 技术指标数据
     */
    @Schema(description = "技术指标数据")
    private Map<String, List<BigDecimal>> technicalIndicators;
    
    /**
     * 统计信息
     */
    @Schema(description = "统计信息")
    private CandlestickStatistics statistics;
    
    /**
     * 数据元信息
     */
    @Schema(description = "数据元信息")
    private DataMetadata metadata;
    
    /**
     * K线统计信息
     */
    @Data
    @Schema(description = "K线统计信息")
    public static class CandlestickStatistics {
        
        /**
         * 总成交量
         */
        @Schema(description = "总成交量", example = "15000")
        private Long totalVolume;
        
        /**
         * 平均成交量
         */
        @Schema(description = "平均成交量", example = "500.00")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal avgVolume;
        
        /**
         * 最高价
         */
        @Schema(description = "最高价", example = "1200.00")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal maxHigh;
        
        /**
         * 最低价
         */
        @Schema(description = "最低价", example = "800.00")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal minLow;
        
        /**
         * 价格变化
         */
        @Schema(description = "价格变化", example = "150.50")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal priceChange;
        
        /**
         * 价格变化百分比
         */
        @Schema(description = "价格变化百分比", example = "15.05")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal priceChangePercent;
        
        /**
         * 上涨天数
         */
        @Schema(description = "上涨天数", example = "18")
        private Integer riseDays;
        
        /**
         * 下跌天数
         */
        @Schema(description = "下跌天数", example = "10")
        private Integer fallDays;
        
        /**
         * 平盘天数
         */
        @Schema(description = "平盘天数", example = "2")
        private Integer flatDays;
        
        /**
         * 上涨概率
         */
        @Schema(description = "上涨概率", example = "60.00")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal riseRate;
        
        /**
         * 最大单日涨幅
         */
        @Schema(description = "最大单日涨幅", example = "8.50")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal maxDailyRise;
        
        /**
         * 最大单日跌幅
         */
        @Schema(description = "最大单日跌幅", example = "-6.20")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal maxDailyFall;
        
        /**
         * 平均振幅
         */
        @Schema(description = "平均振幅", example = "4.25")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal avgAmplitude;
    }
    
    /**
     * 数据元信息
     */
    @Data
    @Schema(description = "数据元信息")
    public static class DataMetadata {
        
        /**
         * 数据类型
         */
        @Schema(description = "数据类型", example = "revenue")
        private String dataType;
        
        /**
         * 时间粒度
         */
        @Schema(description = "时间粒度", example = "day")
        private String timeframe;
        
        /**
         * 数据点数量
         */
        @Schema(description = "数据点数量", example = "30")
        private Integer dataPointCount;
        
        /**
         * 查询开始时间
         */
        @Schema(description = "查询开始时间", example = "2025-06-01 00:00:00")
        private String queryStartTime;
        
        /**
         * 查询结束时间
         */
        @Schema(description = "查询结束时间", example = "2025-08-31 23:59:59")
        private String queryEndTime;
        
        /**
         * 实际数据开始时间
         */
        @Schema(description = "实际数据开始时间", example = "2025-06-01")
        private String actualStartDate;
        
        /**
         * 实际数据结束时间
         */
        @Schema(description = "实际数据结束时间", example = "2025-08-30")
        private String actualEndDate;
        
        /**
         * 是否包含技术指标
         */
        @Schema(description = "是否包含技术指标", example = "true")
        private Boolean includeTechnicalIndicators;
        
        /**
         * 技术指标列表
         */
        @Schema(description = "技术指标列表")
        private List<String> technicalIndicatorList;
        
        /**
         * 数据生成时间
         */
        @Schema(description = "数据生成时间", example = "2025-08-18 10:30:00")
        private String generatedAt;
        
        /**
         * 数据来源
         */
        @Schema(description = "数据来源", example = "vim_order_recharge")
        private String dataSource;
        
        /**
         * 是否降采样
         */
        @Schema(description = "是否降采样", example = "false")
        private Boolean isDownsampled;
        
        /**
         * 原始数据点数量
         */
        @Schema(description = "原始数据点数量", example = "30")
        private Integer originalDataPointCount;
    }
}
