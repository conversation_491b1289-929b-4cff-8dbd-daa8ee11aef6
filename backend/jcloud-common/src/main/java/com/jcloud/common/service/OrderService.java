package com.jcloud.common.service;

import com.jcloud.common.dto.BatchSettleRequest;
import com.jcloud.common.dto.OrderCreateRequest;
import com.jcloud.common.dto.OrderQueryRequest;
import com.jcloud.common.entity.SysOrder;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.vo.OrderVO;

import java.util.List;

/**
 * 佣金结算订单服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderService {
    
    /**
     * 创建佣金结算订单
     *
     * @param request 订单创建请求
     * @return 创建的订单列表（主订单+子订单）
     */
    List<SysOrder> createOrders(OrderCreateRequest request);

    /**
     * 分页查询订单列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    PageResult<OrderVO> pageOrders(OrderQueryRequest request);

    /**
     * 查询订单详情
     *
     * @param payid 订单ID
     * @return 订单详情
     */
    OrderVO getOrderDetail(String payid);

    /**
     * 查询主订单及其子订单（树形结构）
     *
     * @param parentsPayid 主订单ID
     * @return 主订单及子订单
     */
    OrderVO getMainOrderWithSubOrders(String parentsPayid);

    /**
     * 获取指定主订单的所有子订单
     *
     * @param mainOrderId 主订单ID
     * @return 子订单列表
     */
    List<OrderVO> getSubOrdersByMainOrderId(String mainOrderId);

    /**
     * 批量结算订单
     *
     * @param payids 订单ID列表
     * @return 结算成功的订单数量
     */
    int settleOrders(List<String> payids);

    /**
     * 批量结算订单（带实际结算金额）
     *
     * @param request 批量结算请求
     * @return 结算成功的订单数量
     */
    int settleOrdersWithActualAmount(BatchSettleRequest request);

    /**
     * 取消订单
     *
     * @param payid 订单ID
     * @return 是否成功
     */
    boolean cancelOrder(String payid);

    /**
     * 批量取消订单
     *
     * @param payids 订单ID列表
     * @return 取消成功的订单数量
     */
    int cancelOrders(List<String> payids);

    /**
     * 检查用户是否可以创建订单
     *
     * @param request 订单创建请求
     * @return 检查结果
     */
    boolean canCreateOrder(OrderCreateRequest request);

    /**
     * 获取当前用户的订单统计信息
     *
     * @return 统计信息
     */
    OrderStatistics getOrderStatistics();

    /**
     * 导出订单数据
     *
     * @param request 查询请求
     * @return 导出数据
     */
    List<OrderVO> exportOrders(OrderQueryRequest request);

    /**
     * 导出订单数据为Excel文件
     *
     * @param request 查询请求
     * @return Excel文件字节数组
     */
    byte[] exportOrdersToExcel(OrderQueryRequest request);
    
    /**
     * 订单统计信息内部类
     */
    class OrderStatistics {
        private Long totalOrders;
        private Long pendingOrders;
        private Long settledOrders;
        private Long cancelledOrders;
        
        // getters and setters
        public Long getTotalOrders() { return totalOrders; }
        public void setTotalOrders(Long totalOrders) { this.totalOrders = totalOrders; }
        
        public Long getPendingOrders() { return pendingOrders; }
        public void setPendingOrders(Long pendingOrders) { this.pendingOrders = pendingOrders; }
        
        public Long getSettledOrders() { return settledOrders; }
        public void setSettledOrders(Long settledOrders) { this.settledOrders = settledOrders; }
        
        public Long getCancelledOrders() { return cancelledOrders; }
        public void setCancelledOrders(Long cancelledOrders) { this.cancelledOrders = cancelledOrders; }
    }
}
