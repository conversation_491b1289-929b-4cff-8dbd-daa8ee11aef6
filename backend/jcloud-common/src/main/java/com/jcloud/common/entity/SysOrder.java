package com.jcloud.common.entity;

import com.jcloud.common.enums.OrderStatus;
import com.jcloud.common.enums.SettlementStatus;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 佣金结算订单实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("sys_order")
@Schema(description = "佣金结算订单")
public class SysOrder {
    
    /**
     * 结算订单ID（主键）
     */
    @Id(keyType = KeyType.None)
    @Schema(description = "结算订单ID")
    private String payid;
    
    /**
     * 上级关联订单ID（父订单ID）
     */
    @Schema(description = "上级关联订单ID")
    private String parentsPayid;
    
    /**
     * 代理名称（ID）
     */
    @Schema(description = "代理ID")
    private String agent;
    
    /**
     * 主播名称（ID）
     */
    @Schema(description = "主播ID")
    private String anchor;
    
    /**
     * 自充值金额
     */
    @Schema(description = "自充值金额")
    private BigDecimal amountSelf;
    
    /**
     * 推广充值金额
     */
    @Schema(description = "推广充值金额")
    private BigDecimal amountFans;
    
    /**
     * 劳务比例
     */
    @Schema(description = "劳务比例")
    private BigDecimal feeRate;
    
    /**
     * 推广劳务费
     */
    @Schema(description = "推广劳务费")
    private BigDecimal feeValue;
    
    /**
     * 实际结算金额
     */
    @Schema(description = "实际结算金额")
    private BigDecimal feeActual;

    /**
     * 实际结算金额（用户手动输入）
     */
    @Column("actual_settlement_amount")
    @Schema(description = "实际结算金额（用户手动输入）")
    private BigDecimal actualSettlementAmount;

    /**
     * 收款人
     */
    @Schema(description = "收款人")
    private String feePerson;
    
    /**
     * 收款账号
     */
    @Schema(description = "收款账号")
    private String feeAccount;
    
    /**
     * 是否已结算（兼容旧字段）
     */
    @Schema(description = "是否已结算")
    private String ispay;
    
    /**
     * 订单状态
     */
    @Column("order_status")
    @Schema(description = "订单状态")
    private Integer orderStatus;
    
    /**
     * 结算状态
     */
    @Column("settlement_status")
    @Schema(description = "结算状态")
    private Integer settlementStatus;
    
    /**
     * 来源充值订单ID
     */
    @Column("source_order_id")
    @Schema(description = "来源充值订单ID")
    private String sourceOrderId;
    
    /**
     * vim用户ID
     */
    @Column("vim_user_id")
    @Schema(description = "vim用户ID")
    private Integer vimUserId;

    /**
     * 结算开始时间（时间戳，秒）
     */
    @Column("settlement_start_time")
    @Schema(description = "结算开始时间")
    private Long settlementStartTime;

    /**
     * 结算结束时间（时间戳，秒）
     */
    @Column("settlement_end_time")
    @Schema(description = "结算结束时间")
    private Long settlementEndTime;

    /**
     * 创建时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()")
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 更新时间（时间戳，秒）
     */
    @Column(onInsertValue = "unix_timestamp()", onUpdateValue = "unix_timestamp()")
    @Schema(description = "更新时间")
    private Long updateTime;
    
    // ==================== 业务方法 ====================
    
    /**
     * 获取订单状态枚举
     */
    public OrderStatus getOrderStatusEnum() {
        return OrderStatus.fromCode(this.orderStatus);
    }
    
    /**
     * 设置订单状态枚举
     */
    public void setOrderStatusEnum(OrderStatus orderStatus) {
        this.orderStatus = orderStatus != null ? orderStatus.getCode() : null;
    }
    
    /**
     * 获取结算状态枚举
     */
    public SettlementStatus getSettlementStatusEnum() {
        return SettlementStatus.fromCode(this.settlementStatus);
    }
    
    /**
     * 设置结算状态枚举
     */
    public void setSettlementStatusEnum(SettlementStatus settlementStatus) {
        this.settlementStatus = settlementStatus != null ? settlementStatus.getCode() : null;
    }
    
    /**
     * 判断是否为主订单
     */
    public boolean isMainOrder() {
        return parentsPayid == null || parentsPayid.trim().isEmpty();
    }
    
    /**
     * 判断是否为子订单
     */
    public boolean isSubOrder() {
        return !isMainOrder();
    }
    
    /**
     * 计算总充值金额
     */
    public BigDecimal getTotalAmount() {
        BigDecimal self = amountSelf != null ? amountSelf : BigDecimal.ZERO;
        BigDecimal fans = amountFans != null ? amountFans : BigDecimal.ZERO;
        return self.add(fans);
    }
}
