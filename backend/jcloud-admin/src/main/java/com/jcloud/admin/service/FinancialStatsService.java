package com.jcloud.admin.service;

import com.jcloud.common.dto.FinancialStatsRequest;
import com.jcloud.common.dto.GroupedFinancialStatsResponse;
import com.jcloud.common.dto.CandlestickRequest;
import com.jcloud.common.dto.CandlestickDataResponse;

/**
 * 财务统计服务接口
 * 提供财务数据查询和处理相关的业务方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FinancialStatsService {
    
    /**
     * 获取财务统计数据
     * 根据时间范围和主播数据选项获取分组的财务统计数据
     * 
     * @param request 财务统计查询请求
     * @return 分组的财务统计数据
     * @throws com.jcloud.common.exception.FinancialDataException 当查询失败时抛出
     */
    GroupedFinancialStatsResponse getFinancialStats(FinancialStatsRequest request);
    
    /**
     * 获取今日财务统计数据
     * 获取当天的财务统计数据，使用默认时间范围
     * 
     * @param includeAnchor 是否包含主播数据
     * @return 分组的财务统计数据
     * @throws com.jcloud.common.exception.FinancialDataException 当查询失败时抛出
     */
    GroupedFinancialStatsResponse getTodayFinancialStats(Boolean includeAnchor);
    
    /**
     * 验证时间参数
     * 验证开始时间和结束时间的有效性和合理性
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @throws com.jcloud.common.exception.FinancialDataException 当时间参数无效时抛出
     */
    void validateTimeParams(String startTime, String endTime);
    
    /**
     * 格式化时间参数
     * 将时间字符串格式化为存储过程所需的格式
     * 
     * @param timeParam 时间参数字符串
     * @return 格式化后的时间字符串
     * @throws com.jcloud.common.exception.FinancialDataException 当时间格式无效时抛出
     */
    String formatTimeParam(String timeParam);
    
    /**
     * 构建今日时间范围请求
     * 创建包含今日开始和结束时间的请求对象
     *
     * @param includeAnchor 是否包含主播数据
     * @return 今日时间范围的财务统计请求
     */
    FinancialStatsRequest buildTodayRequest(Boolean includeAnchor);

    /**
     * 获取K线图数据
     * 根据请求参数获取K线图展示所需的OHLC数据和技术指标
     *
     * @param request K线图数据查询请求
     * @return K线图完整数据响应
     * @throws com.jcloud.common.exception.FinancialDataException 当查询失败时抛出
     */
    CandlestickDataResponse getCandlestickData(CandlestickRequest request);
}