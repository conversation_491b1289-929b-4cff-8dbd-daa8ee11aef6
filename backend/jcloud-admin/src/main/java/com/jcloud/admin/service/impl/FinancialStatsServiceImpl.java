package com.jcloud.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.FinancialStatsService;
import com.jcloud.common.dto.FinancialStatsRequest;
import com.jcloud.common.dto.FinancialStatsResponse;
import com.jcloud.common.dto.GroupedFinancialStatsResponse;
import com.jcloud.common.dto.CandlestickRequest;
import com.jcloud.common.dto.CandlestickResponse;
import com.jcloud.common.dto.CandlestickDataResponse;
import com.jcloud.common.exception.FinancialDataException;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.FinancialStatsMapper;
import com.jcloud.common.mapper.CandlestickMapper;
import com.jcloud.common.util.TechnicalIndicatorUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import com.jcloud.common.util.TimeUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 财务统计服务实现类
 * 提供财务数据查询和处理的具体实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinancialStatsServiceImpl implements FinancialStatsService {

    // 常量定义
    private static final String TIME_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final List<String> SUPPORTED_DATA_TYPES = List.of("revenue", "profit", "commission");
    private static final List<String> SUPPORTED_TIMEFRAMES = List.of("day", "week", "month");
    private static final int DEFAULT_MAX_DATA_POINTS = 1000;
    private static final int MAX_QUERY_YEARS = 1;

    private final FinancialStatsMapper financialStatsMapper;
    private final CandlestickMapper candlestickMapper;
    
    /**
     * 时间格式模式
     */
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 统计分类常量
     */
    private static final String USER_STATS_CATEGORY = "用户相关统计";
    private static final String ANCHOR_STATS_CATEGORY = "主播相关统计";
    private static final String TOTAL_STATS_CATEGORY = "合计统计";
    private static final String BUSINESS_STATS_CATEGORY = "其他业务统计";
    
    @Override
    public GroupedFinancialStatsResponse getFinancialStats(FinancialStatsRequest request) {
        log.info("开始获取财务统计数据，请求参数：{}", request);
        
        try {
            // 参数验证
            validateTimeParams(request.getStartTime(), request.getEndTime());
            
            // 格式化时间参数
            String formattedStartTime = formatTimeParam(request.getStartTime());
            String formattedEndTime = formatTimeParam(request.getEndTime());
            
            log.debug("格式化后的时间参数 - 开始时间：{}，结束时间：{}", formattedStartTime, formattedEndTime);
            
            // 调用存储过程获取原始数据
            List<FinancialStatsResponse> rawData = financialStatsMapper.getStatisticsReport(
                formattedStartTime,
                formattedEndTime,
                request.getIncludeAnchor()
            );
            
            log.info("存储过程返回数据条数：{}", rawData != null ? rawData.size() : 0);
            
            // 数据分组处理
            GroupedFinancialStatsResponse response = groupStatsByCategory(rawData);
            
            log.info("财务统计数据获取成功");
            return response;
            
        } catch (DataAccessException e) {
            log.error("数据库访问异常，获取财务统计数据失败", e);
            throw FinancialDataException.procedureCallError("存储过程调用失败：" + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取财务统计数据时发生未知异常", e);
            throw new FinancialDataException("获取财务统计数据失败：" + e.getMessage(), e);
        }
    }
    
    @Override
    public GroupedFinancialStatsResponse getTodayFinancialStats(Boolean includeAnchor) {
        log.info("开始获取今日财务统计数据，是否包含主播数据：{}", includeAnchor);
        
        FinancialStatsRequest request = buildTodayRequest(includeAnchor);
        return getFinancialStats(request);
    }
    
    @Override
    public void validateTimeParams(String startTime, String endTime) {
        // 检查时间参数是否为空
        if (StrUtil.isBlank(startTime)) {
            throw FinancialDataException.parameterValidationError("开始时间不能为空");
        }
        if (StrUtil.isBlank(endTime)) {
            throw FinancialDataException.parameterValidationError("结束时间不能为空");
        }
        
        try {
            // 解析时间字符串为时间戳
            Long startTimestamp = TimeUtil.parse(startTime, DATE_TIME_PATTERN);
            Long endTimestamp = TimeUtil.parse(endTime, DATE_TIME_PATTERN);

            // 验证时间范围的合理性
            if (startTimestamp > endTimestamp) {
                throw FinancialDataException.parameterValidationError("开始时间不能晚于结束时间");
            }

            // 验证时间范围不能超过1年（365天 * 24小时 * 3600秒）
            long oneYearInSeconds = 365L * 24 * 3600;
            if (endTimestamp - startTimestamp > oneYearInSeconds) {
                throw FinancialDataException.parameterValidationError("查询时间范围不能超过1年");
            }
        } catch (Exception e) {
            log.error("时间参数格式错误：startTime={}, endTime={}", startTime, endTime, e);
            throw FinancialDataException.parameterValidationError("时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
    }
    
    @Override
    public String formatTimeParam(String timeParam) {
        if (StrUtil.isBlank(timeParam)) {
            throw FinancialDataException.parameterValidationError("时间参数不能为空");
        }
        
        try {
            // 解析并重新格式化时间，确保格式统一
            Long timestamp = TimeUtil.parse(timeParam, DATE_TIME_PATTERN);
            return TimeUtil.format(timestamp, DATE_TIME_PATTERN);
        } catch (Exception e) {
            log.error("时间参数格式化失败：{}", timeParam, e);
            throw FinancialDataException.parameterValidationError("时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
    }
    
    @Override
    public FinancialStatsRequest buildTodayRequest(Boolean includeAnchor) {
        Long now = TimeUtil.now();
        Long startOfDay = TimeUtil.parse(TimeUtil.format(now, "yyyy-MM-dd") + " 00:00:00", DATE_TIME_PATTERN);
        Long endOfDay = TimeUtil.parse(TimeUtil.format(now, "yyyy-MM-dd") + " 23:59:59", DATE_TIME_PATTERN);

        FinancialStatsRequest request = new FinancialStatsRequest();
        request.setStartTime(TimeUtil.format(startOfDay, DATE_TIME_PATTERN));
        request.setEndTime(TimeUtil.format(endOfDay, DATE_TIME_PATTERN));
        request.setIncludeAnchor(includeAnchor != null ? includeAnchor : true);

        log.debug("构建今日时间范围请求：{}", request);
        return request;
    }
    
    /**
     * 将原始统计数据按分类进行分组
     * 
     * @param rawData 原始统计数据列表
     * @return 分组后的财务统计数据
     */
    private GroupedFinancialStatsResponse groupStatsByCategory(List<FinancialStatsResponse> rawData) {
        GroupedFinancialStatsResponse response = new GroupedFinancialStatsResponse();
        
        if (rawData == null || rawData.isEmpty()) {
            log.warn("原始统计数据为空，返回空的分组数据");
            response.setUserStats(new ArrayList<>());
            response.setAnchorStats(new ArrayList<>());
            response.setTotalStats(new ArrayList<>());
            response.setBusinessStats(new ArrayList<>());
            return response;
        }
        
        // 按分类分组数据
        response.setUserStats(filterStatsByCategory(rawData, USER_STATS_CATEGORY));
        response.setAnchorStats(filterStatsByCategory(rawData, ANCHOR_STATS_CATEGORY));
        response.setTotalStats(filterStatsByCategory(rawData, TOTAL_STATS_CATEGORY));
        response.setBusinessStats(filterStatsByCategory(rawData, BUSINESS_STATS_CATEGORY));
        
        log.debug("数据分组完成 - 用户相关：{}条，主播相关：{}条，合计统计：{}条，其他业务：{}条",
            response.getUserStats().size(),
            response.getAnchorStats().size(),
            response.getTotalStats().size(),
            response.getBusinessStats().size());
        
        return response;
    }
    
    /**
     * 根据分类过滤统计数据
     *
     * @param rawData 原始数据列表
     * @param category 分类名称
     * @return 过滤后的数据列表
     */
    private List<FinancialStatsResponse> filterStatsByCategory(List<FinancialStatsResponse> rawData, String category) {
        return rawData.stream()
            .filter(item -> category.equals(item.getCategory()))
            .collect(Collectors.toList());
    }

    @Override
    public CandlestickDataResponse getCandlestickData(CandlestickRequest request) {
        log.info("开始获取K线图数据，请求参数：{}", request);

        try {
            // 1. 参数验证
            validateCandlestickRequest(request);

            // 2. 转换时间格式
            Integer startTime = convertToTimestamp(request.getStartTime());
            Integer endTime = convertToTimestamp(request.getEndTime());

            // 3. 根据数据类型查询K线数据
            List<CandlestickResponse> candleData = queryCandlestickData(request, startTime, endTime);

            // 4. 计算涨跌额和涨跌幅
            calculateChangeAndPercent(candleData);

            // 5. 数据降采样（如果需要）
            candleData = downsampleIfNeeded(candleData, request.getMaxDataPoints());

            // 6. 计算技术指标（如果需要）
            Map<String, List<BigDecimal>> technicalIndicators = new HashMap<>();
            if (Boolean.TRUE.equals(request.getIncludeTechnicalIndicators())) {
                technicalIndicators = calculateTechnicalIndicators(candleData, request.getTechnicalIndicators());
            }

            // 7. 计算统计信息
            CandlestickDataResponse.CandlestickStatistics statistics =
                calculateCandlestickStatistics(candleData);

            // 8. 构建元数据
            CandlestickDataResponse.DataMetadata metadata =
                buildDataMetadata(request, candleData);

            // 9. 构建响应
            CandlestickDataResponse response = new CandlestickDataResponse();
            response.setCandleData(candleData);
            response.setTechnicalIndicators(technicalIndicators);
            response.setStatistics(statistics);
            response.setMetadata(metadata);

            log.info("K线图数据获取成功，数据点数量: {}", candleData.size());
            return response;

        } catch (BusinessException e) {
            // 业务异常直接抛出
            throw e;
        } catch (DataAccessException e) {
            log.error("数据库访问异常", e);
            throw new BusinessException("数据查询失败，请稍后重试");
        } catch (Exception e) {
            log.error("获取K线图数据时发生未知异常", e);
            throw new BusinessException("系统异常，请联系管理员");
        }
    }

    /**
     * 验证K线图请求参数
     */
    private void validateCandlestickRequest(CandlestickRequest request) {
        if (StrUtil.isBlank(request.getStartTime())) {
            throw new BusinessException("开始时间不能为空");
        }
        if (StrUtil.isBlank(request.getEndTime())) {
            throw new BusinessException("结束时间不能为空");
        }
        if (StrUtil.isBlank(request.getTimeframe())) {
            throw new BusinessException("时间粒度不能为空");
        }
        if (StrUtil.isBlank(request.getDataType())) {
            throw new BusinessException("数据类型不能为空");
        }

        // 验证时间格式和逻辑
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_FORMAT_PATTERN);
            LocalDateTime start = LocalDateTime.parse(request.getStartTime(), formatter);
            LocalDateTime end = LocalDateTime.parse(request.getEndTime(), formatter);

            if (start.isAfter(end)) {
                throw new BusinessException("开始时间不能晚于结束时间");
            }

            // 验证时间范围不能超过指定年限
            if (start.plusYears(MAX_QUERY_YEARS).isBefore(end)) {
                throw new BusinessException("查询时间范围不能超过" + MAX_QUERY_YEARS + "年");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("时间格式不正确，请使用 " + TIME_FORMAT_PATTERN + " 格式");
        }

        // 验证数据类型
        if (!SUPPORTED_DATA_TYPES.contains(request.getDataType())) {
            throw new BusinessException("不支持的数据类型：" + request.getDataType());
        }

        // 验证时间粒度
        if (!SUPPORTED_TIMEFRAMES.contains(request.getTimeframe())) {
            throw new BusinessException("不支持的时间粒度：" + request.getTimeframe());
        }
    }

    /**
     * 转换时间字符串为时间戳
     */
    private Integer convertToTimestamp(String timeStr) {
        try {
            return (int) (DateUtil.parse(timeStr, "yyyy-MM-dd HH:mm:ss").getTime() / 1000);
        } catch (Exception e) {
            throw new BusinessException("时间格式转换失败：" + timeStr);
        }
    }

    /**
     * 根据数据类型查询K线数据
     */
    private List<CandlestickResponse> queryCandlestickData(CandlestickRequest request, Integer startTime, Integer endTime) {
        String dataType = request.getDataType();
        String timeframe = request.getTimeframe();

        switch (dataType) {
            case "revenue":
                return candlestickMapper.selectRevenueKlineData(startTime, endTime, timeframe);
            case "profit":
                return candlestickMapper.selectProfitKlineData(startTime, endTime, timeframe);
            case "commission":
                return candlestickMapper.selectCommissionKlineData(startTime, endTime, timeframe);
            default:
                throw new BusinessException("不支持的数据类型: " + dataType);
        }
    }

    /**
     * 计算涨跌额和涨跌幅
     */
    private void calculateChangeAndPercent(List<CandlestickResponse> candleData) {
        for (CandlestickResponse candle : candleData) {
            if (candle.getOpen() != null && candle.getClose() != null) {
                // 计算涨跌额
                BigDecimal change = candle.getClose().subtract(candle.getOpen());
                candle.setChange(change);

                // 计算涨跌幅
                if (candle.getOpen().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal changePercent = change.divide(candle.getOpen(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                    candle.setChangePercent(changePercent);
                } else {
                    candle.setChangePercent(BigDecimal.ZERO);
                }

                // 计算振幅
                if (candle.getHigh() != null && candle.getLow() != null && candle.getOpen().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal amplitude = candle.getHigh().subtract(candle.getLow())
                        .divide(candle.getOpen(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                    candle.setAmplitude(amplitude);
                } else {
                    candle.setAmplitude(BigDecimal.ZERO);
                }
            }
        }
    }

    /**
     * 数据降采样
     */
    private List<CandlestickResponse> downsampleIfNeeded(List<CandlestickResponse> candleData, Integer maxDataPoints) {
        if (maxDataPoints == null || candleData.size() <= maxDataPoints) {
            return candleData;
        }

        log.info("数据点数量 {} 超过最大限制 {}，开始降采样", candleData.size(), maxDataPoints);

        int step = candleData.size() / maxDataPoints;
        if (step <= 1) {
            return candleData;
        }

        List<CandlestickResponse> sampledData = new ArrayList<>();
        for (int i = 0; i < candleData.size(); i += step) {
            sampledData.add(candleData.get(i));
        }

        // 确保包含最后一个数据点
        if (!sampledData.contains(candleData.get(candleData.size() - 1))) {
            sampledData.add(candleData.get(candleData.size() - 1));
        }

        log.info("降采样完成，数据点数量从 {} 减少到 {}", candleData.size(), sampledData.size());
        return sampledData;
    }

    /**
     * 计算技术指标
     */
    private Map<String, List<BigDecimal>> calculateTechnicalIndicators(
            List<CandlestickResponse> candleData, List<String> indicators) {

        // 提取收盘价
        List<BigDecimal> closePrices = candleData.stream()
            .map(CandlestickResponse::getClose)
            .collect(Collectors.toList());

        // 使用工具类计算技术指标
        return TechnicalIndicatorUtil.calculateAllIndicators(closePrices, indicators);
    }

    /**
     * 计算K线统计信息
     */
    private CandlestickDataResponse.CandlestickStatistics calculateCandlestickStatistics(
            List<CandlestickResponse> candleData) {

        CandlestickDataResponse.CandlestickStatistics statistics =
            new CandlestickDataResponse.CandlestickStatistics();

        if (candleData == null || candleData.isEmpty()) {
            return statistics;
        }

        // 基础统计
        long totalVolume = candleData.stream()
            .mapToLong(CandlestickResponse::getVolume)
            .sum();
        statistics.setTotalVolume(totalVolume);

        BigDecimal avgVolume = BigDecimal.valueOf(totalVolume)
            .divide(new BigDecimal(candleData.size()), 2, RoundingMode.HALF_UP);
        statistics.setAvgVolume(avgVolume);

        // 价格统计
        BigDecimal maxHigh = candleData.stream()
            .map(CandlestickResponse::getHigh)
            .filter(java.util.Objects::nonNull)
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.ZERO);
        statistics.setMaxHigh(maxHigh);

        BigDecimal minLow = candleData.stream()
            .map(CandlestickResponse::getLow)
            .filter(java.util.Objects::nonNull)
            .min(BigDecimal::compareTo)
            .orElse(BigDecimal.ZERO);
        statistics.setMinLow(minLow);

        // 价格变化
        if (candleData.size() >= 2) {
            BigDecimal firstClose = candleData.get(0).getClose();
            BigDecimal lastClose = candleData.get(candleData.size() - 1).getClose();
            if (firstClose != null && lastClose != null) {
                BigDecimal priceChange = lastClose.subtract(firstClose);
                statistics.setPriceChange(priceChange);

                if (firstClose.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal priceChangePercent = priceChange
                        .divide(firstClose, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                    statistics.setPriceChangePercent(priceChangePercent);
                } else {
                    statistics.setPriceChangePercent(BigDecimal.ZERO);
                }
            }
        }

        // 涨跌统计
        int riseDays = 0;
        int fallDays = 0;
        int flatDays = 0;
        BigDecimal maxDailyRise = BigDecimal.ZERO;
        BigDecimal maxDailyFall = BigDecimal.ZERO;
        BigDecimal totalAmplitude = BigDecimal.ZERO;

        for (CandlestickResponse candle : candleData) {
            if (candle.getOpen() != null && candle.getClose() != null) {
                BigDecimal change = candle.getClose().subtract(candle.getOpen());

                if (change.compareTo(BigDecimal.ZERO) > 0) {
                    riseDays++;
                    if (candle.getChangePercent() != null &&
                        candle.getChangePercent().compareTo(maxDailyRise) > 0) {
                        maxDailyRise = candle.getChangePercent();
                    }
                } else if (change.compareTo(BigDecimal.ZERO) < 0) {
                    fallDays++;
                    if (candle.getChangePercent() != null &&
                        candle.getChangePercent().compareTo(maxDailyFall) < 0) {
                        maxDailyFall = candle.getChangePercent();
                    }
                } else {
                    flatDays++;
                }

                if (candle.getAmplitude() != null) {
                    totalAmplitude = totalAmplitude.add(candle.getAmplitude());
                }
            }
        }

        statistics.setRiseDays(riseDays);
        statistics.setFallDays(fallDays);
        statistics.setFlatDays(flatDays);
        statistics.setMaxDailyRise(maxDailyRise);
        statistics.setMaxDailyFall(maxDailyFall);

        // 上涨概率
        int totalDays = candleData.size();
        if (totalDays > 0) {
            BigDecimal riseRate = new BigDecimal(riseDays)
                .divide(new BigDecimal(totalDays), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
            statistics.setRiseRate(riseRate);
        }

        // 平均振幅
        if (totalDays > 0 && totalAmplitude.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal avgAmplitude = totalAmplitude
                .divide(new BigDecimal(totalDays), 4, RoundingMode.HALF_UP);
            statistics.setAvgAmplitude(avgAmplitude);
        } else {
            statistics.setAvgAmplitude(BigDecimal.ZERO);
        }

        return statistics;
    }

    /**
     * 构建数据元信息
     */
    private CandlestickDataResponse.DataMetadata buildDataMetadata(
            CandlestickRequest request, List<CandlestickResponse> candleData) {

        CandlestickDataResponse.DataMetadata metadata =
            new CandlestickDataResponse.DataMetadata();

        metadata.setDataType(request.getDataType());
        metadata.setTimeframe(request.getTimeframe());
        metadata.setDataPointCount(candleData.size());
        metadata.setQueryStartTime(request.getStartTime());
        metadata.setQueryEndTime(request.getEndTime());

        // 实际数据时间范围
        if (!candleData.isEmpty()) {
            metadata.setActualStartDate(candleData.get(0).getDate());
            metadata.setActualEndDate(candleData.get(candleData.size() - 1).getDate());
        }

        metadata.setIncludeTechnicalIndicators(
            Boolean.TRUE.equals(request.getIncludeTechnicalIndicators()));
        metadata.setTechnicalIndicatorList(request.getTechnicalIndicators());

        // 生成时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        metadata.setGeneratedAt(LocalDateTime.now().format(formatter));

        // 数据来源
        switch (request.getDataType()) {
            case "revenue":
                metadata.setDataSource("vim_order_recharge");
                break;
            case "profit":
                metadata.setDataSource("vim_order_recharge + sys_order");
                break;
            case "commission":
                metadata.setDataSource("sys_order");
                break;
            default:
                metadata.setDataSource("unknown");
        }

        // 降采样信息
        Integer maxDataPoints = request.getMaxDataPoints();
        if (maxDataPoints != null && candleData.size() > maxDataPoints) {
            metadata.setIsDownsampled(true);
            metadata.setOriginalDataPointCount(candleData.size());
        } else {
            metadata.setIsDownsampled(false);
            metadata.setOriginalDataPointCount(candleData.size());
        }

        return metadata;
    }
}