-- K线图功能数据库索引优化建议
-- 执行这些索引可以显著提升K线图查询性能

-- 1. vim_order_recharge表索引优化
-- 为状态和创建时间添加复合索引
CREATE INDEX idx_vim_order_recharge_state_time ON vim_order_recharge(state, create_time);

-- 为用户ID和创建时间添加复合索引（用于用户维度分析）
CREATE INDEX idx_vim_order_recharge_uid_time ON vim_order_recharge(uid, create_time);

-- 2. sys_order表索引优化
-- 为创建时间和费用值添加复合索引
CREATE INDEX idx_sys_order_time_fee ON sys_order(create_time, fee_value);

-- 为用户ID和创建时间添加复合索引
CREATE INDEX idx_sys_order_user_time ON sys_order(user_id, create_time);

-- 为源订单ID添加索引（用于关联查询）
CREATE INDEX idx_sys_order_source_id ON sys_order(source_order_id);

-- 3. 分区表建议（可选，适用于大数据量场景）
-- 如果数据量超过百万级别，建议按月分区

-- vim_order_recharge按月分区示例
-- ALTER TABLE vim_order_recharge PARTITION BY RANGE (create_time) (
--     PARTITION p202501 VALUES LESS THAN (UNIX_TIMESTAMP('2025-02-01')),
--     PARTITION p202502 VALUES LESS THAN (UNIX_TIMESTAMP('2025-03-01')),
--     PARTITION p202503 VALUES LESS THAN (UNIX_TIMESTAMP('2025-04-01')),
--     -- 继续添加更多分区...
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 4. 查询性能监控
-- 使用以下查询监控K线图相关查询的性能
-- EXPLAIN SELECT ... FROM vim_order_recharge WHERE state = 2 AND create_time >= ? AND create_time <= ?;

-- 5. 定期维护建议
-- 定期分析表统计信息
-- ANALYZE TABLE vim_order_recharge;
-- ANALYZE TABLE sys_order;

-- 6. 配置优化建议
-- 在MySQL配置中增加以下参数以优化GROUP_CONCAT性能：
-- group_concat_max_len = 1048576  # 增加GROUP_CONCAT最大长度
-- sort_buffer_size = 2M           # 增加排序缓冲区大小
-- tmp_table_size = 64M            # 增加临时表大小
-- max_heap_table_size = 64M       # 增加内存表大小
