# K线图API文档

## 概述

K线图API提供财务数据的K线图展示功能，支持收入、利润、佣金三种数据类型的K线分析，以及多种技术指标计算。

## API接口

### 获取K线图数据

**接口地址：** `POST /financial/stats/candlestick-data`

**权限要求：** `financial:stats:view`

**请求参数：**

```json
{
  "startTime": "2025-06-01 00:00:00",
  "endTime": "2025-08-31 23:59:59",
  "timeframe": "day",
  "dataType": "revenue",
  "includeTechnicalIndicators": true,
  "technicalIndicators": ["MA5", "MA20", "BOLL", "RSI"],
  "maxDataPoints": 1000
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startTime | String | 是 | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | 是 | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| timeframe | String | 是 | 时间粒度：day/week/month |
| dataType | String | 是 | 数据类型：revenue/profit/commission |
| includeTechnicalIndicators | Boolean | 否 | 是否包含技术指标，默认false |
| technicalIndicators | Array | 否 | 技术指标列表 |
| maxDataPoints | Integer | 否 | 最大数据点数，默认1000 |

**数据类型说明：**

- `revenue`: 收入K线，基于充值订单数据
- `profit`: 利润K线，收入减去佣金支出
- `commission`: 佣金K线，基于佣金结算订单数据

**时间粒度说明：**

- `day`: 日K线，按天聚合数据
- `week`: 周K线，按周聚合数据
- `month`: 月K线，按月聚合数据

**技术指标说明：**

- `MA5`: 5日移动平均线
- `MA10`: 10日移动平均线
- `MA20`: 20日移动平均线
- `MA60`: 60日移动平均线
- `BOLL`: 布林带（包含上轨、中轨、下轨）
- `RSI`: 相对强弱指数

**响应示例：**

```json
{
  "code": 200,
  "success": true,
  "message": "获取K线图数据成功",
  "data": {
    "candleData": [
      {
        "date": "2025-06-01",
        "open": 1000.00,
        "close": 1050.00,
        "low": 980.00,
        "high": 1080.00,
        "volume": 150,
        "amount": 157500.00,
        "change": 50.00,
        "changePercent": 5.00,
        "amplitude": 10.00,
        "turnoverRate": 2.50
      }
    ],
    "technicalIndicators": {
      "MA5": [null, null, null, null, 1025.00],
      "MA20": [null, null, null, null, null],
      "BOLL_UPPER": [null, null, null, null, 1100.00],
      "BOLL_MIDDLE": [null, null, null, null, 1025.00],
      "BOLL_LOWER": [null, null, null, null, 950.00],
      "RSI": [null, null, null, null, 65.50]
    },
    "statistics": {
      "totalVolume": 15000,
      "avgVolume": 500.00,
      "maxHigh": 1200.00,
      "minLow": 800.00,
      "priceChange": 150.50,
      "priceChangePercent": 15.05,
      "riseDays": 18,
      "fallDays": 10,
      "flatDays": 2,
      "riseRate": 60.00,
      "maxDailyRise": 8.50,
      "maxDailyFall": -6.20,
      "avgAmplitude": 4.25
    },
    "metadata": {
      "dataType": "revenue",
      "timeframe": "day",
      "dataPointCount": 30,
      "queryStartTime": "2025-06-01 00:00:00",
      "queryEndTime": "2025-08-31 23:59:59",
      "actualStartDate": "2025-06-01",
      "actualEndDate": "2025-08-30",
      "includeTechnicalIndicators": true,
      "technicalIndicatorList": ["MA5", "MA20", "BOLL", "RSI"],
      "generatedAt": "2025-08-18 10:30:00",
      "dataSource": "vim_order_recharge",
      "isDownsampled": false,
      "originalDataPointCount": 30
    }
  }
}
```

## 使用示例

### 1. 获取收入日K线数据

```bash
curl -X POST "http://localhost:8081/financial/stats/candlestick-data" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "startTime": "2025-07-01 00:00:00",
    "endTime": "2025-07-31 23:59:59",
    "timeframe": "day",
    "dataType": "revenue",
    "includeTechnicalIndicators": true,
    "technicalIndicators": ["MA5", "MA20"]
  }'
```

### 2. 获取利润周K线数据

```bash
curl -X POST "http://localhost:8081/financial/stats/candlestick-data" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "startTime": "2025-06-01 00:00:00",
    "endTime": "2025-08-31 23:59:59",
    "timeframe": "week",
    "dataType": "profit",
    "includeTechnicalIndicators": false
  }'
```

### 3. 获取佣金月K线数据

```bash
curl -X POST "http://localhost:8081/financial/stats/candlestick-data" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "startTime": "2025-01-01 00:00:00",
    "endTime": "2025-12-31 23:59:59",
    "timeframe": "month",
    "dataType": "commission",
    "includeTechnicalIndicators": true,
    "technicalIndicators": ["MA5", "MA20", "BOLL", "RSI"]
  }'
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 注意事项

1. **时间格式**：所有时间参数必须使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **数据量限制**：单次查询最多返回1000个数据点，超出部分会自动降采样
3. **技术指标**：技术指标计算需要足够的数据点，建议至少20个数据点
4. **权限控制**：需要 `financial:stats:view` 权限才能访问
5. **性能考虑**：大时间范围查询可能较慢，建议合理设置时间范围

## 业务价值

1. **趋势分析**：通过K线图直观展示财务数据的变化趋势
2. **波动性分析**：通过影线长度判断数据的稳定性
3. **技术分析**：支持多种技术指标，辅助决策分析
4. **异常检测**：快速识别异常的数据波动
5. **对比分析**：支持不同数据类型的对比分析
