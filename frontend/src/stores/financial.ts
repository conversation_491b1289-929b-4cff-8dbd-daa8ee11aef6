import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { FinancialService } from '@/services'
import type {
  FinancialStatsRequest,
  GroupedFinancialStatsResponse,
} from '@/types'
import type {CandlestickDataPoint, CandlestickDataResponse, CandlestickRequest} from "@/types/candlestick.ts";

/**
 * 财务数据状态接口
 */
interface FinancialState {
  // ==================== 基础财务统计状态 ====================
  
  // 财务统计数据
  statsData: GroupedFinancialStatsResponse | null
  statsLoading: boolean
  statsError: string | null
  
  // 查询参数
  timeRange: {
    startTime: string
    endTime: string
  }
  includeAnchor: boolean
  
  // ==================== K线图相关状态 ====================
  
  // K线图数据
  candlestickData: CandlestickDataResponse | null
  candlestickLoading: boolean
  candlestickError: string | null
  
  // K线图配置
  candlestickTimeframe: 'day' | 'week' | 'month'
  candlestickDataType: 'revenue' | 'profit' | 'commission'
  showTechnicalIndicators: boolean
  selectedTechnicalIndicators: string[]
  
  // 图表交互状态
  chartZoomRange: [number, number] | null
  selectedDataPoint: CandlestickDataPoint | null
  
  // ==================== 基础财务统计操作 ====================
  
  /**
   * 获取财务统计数据
   */
  fetchStatsData: (request: FinancialStatsRequest) => Promise<void>
  
  /**
   * 获取今日财务数据
   */
  fetchTodayStats: (includeAnchor?: boolean) => Promise<void>
  
  /**
   * 设置时间范围
   */
  setTimeRange: (range: { startTime: string; endTime: string }) => void
  
  /**
   * 设置是否包含主播数据
   */
  setIncludeAnchor: (include: boolean) => void
  
  /**
   * 清除财务统计错误
   */
  clearStatsError: () => void
  
  // ==================== K线图操作 ====================
  
  /**
   * 获取K线图数据
   */
  fetchCandlestickData: (request?: Partial<CandlestickRequest>) => Promise<void>
  
  /**
   * 设置K线图时间粒度
   */
  setCandlestickTimeframe: (timeframe: 'day' | 'week' | 'month') => void
  
  /**
   * 设置K线图数据类型
   */
  setCandlestickDataType: (dataType: 'revenue' | 'profit' | 'commission') => void
  
  /**
   * 切换技术指标显示
   */
  toggleTechnicalIndicators: (show?: boolean) => void
  
  /**
   * 设置选中的技术指标
   */
  setSelectedTechnicalIndicators: (indicators: string[]) => void
  
  /**
   * 设置图表缩放范围
   */
  setChartZoomRange: (range: [number, number] | null) => void
  
  /**
   * 设置选中的数据点
   */
  setSelectedDataPoint: (dataPoint: CandlestickDataPoint | null) => void
  
  /**
   * 清除K线图错误
   */
  clearCandlestickError: () => void
  
  /**
   * 重置K线图状态
   */
  resetCandlestickState: () => void
  
  // ==================== 通用操作 ====================
  
  /**
   * 清除所有错误
   */
  clearAllErrors: () => void
  
  /**
   * 重置所有状态
   */
  resetAllState: () => void
}

/**
 * 财务数据状态管理
 */
export const useFinancialStore = create<FinancialState>()(
  devtools(
    (set, get) => ({
      // ==================== 初始状态 ====================
      
      // 基础财务统计状态
      statsData: null,
      statsLoading: false,
      statsError: null,
      timeRange: {
        startTime: '',
        endTime: ''
      },
      includeAnchor: true,
      
      // K线图状态
      candlestickData: null,
      candlestickLoading: false,
      candlestickError: null,
      candlestickTimeframe: 'day',
      candlestickDataType: 'revenue',
      showTechnicalIndicators: false,
      selectedTechnicalIndicators: ['MA5', 'MA20'],
      chartZoomRange: null,
      selectedDataPoint: null,
      
      // ==================== 基础财务统计操作实现 ====================
      
      fetchStatsData: async (request) => {
        set({ statsLoading: true, statsError: null })
        try {
          const data = await FinancialService.getFinancialStats(request)
          set({ 
            statsData: data, 
            statsLoading: false,
            timeRange: {
              startTime: request.startTime,
              endTime: request.endTime
            },
            includeAnchor: request.includeAnchor ?? true
          })
        } catch (error) {
          set({ 
            statsError: error instanceof Error ? error.message : '获取财务数据失败',
            statsLoading: false 
          })
        }
      },
      
      fetchTodayStats: async (includeAnchor = true) => {
        set({ statsLoading: true, statsError: null })
        try {
          const data = await FinancialService.getTodayStats(includeAnchor)
          set({ 
            statsData: data, 
            statsLoading: false,
            includeAnchor
          })
        } catch (error) {
          set({ 
            statsError: error instanceof Error ? error.message : '获取今日财务数据失败',
            statsLoading: false 
          })
        }
      },
      
      setTimeRange: (range) => set({ timeRange: range }),
      
      setIncludeAnchor: (include) => set({ includeAnchor: include }),
      
      clearStatsError: () => set({ statsError: null }),
      
      // ==================== K线图操作实现 ====================
      
      fetchCandlestickData: async (requestOverrides = {}) => {
        set({ candlestickLoading: true, candlestickError: null })
        try {
          const state = get()
          const request = FinancialService.buildCandlestickRequest({
            startTime: state.timeRange.startTime,
            endTime: state.timeRange.endTime,
            timeframe: state.candlestickTimeframe,
            dataType: state.candlestickDataType,
            includeTechnicalIndicators: state.showTechnicalIndicators,
            technicalIndicators: state.selectedTechnicalIndicators,
            ...requestOverrides
          })
          
          // 客户端验证
          const validation = FinancialService.validateCandlestickRequest(request)
          if (!validation.valid) {
            throw new Error(validation.error)
          }
          
          const data = await FinancialService.getCandlestickData(request)
          set({ 
            candlestickData: data, 
            candlestickLoading: false 
          })
        } catch (error) {
          set({ 
            candlestickError: error instanceof Error ? error.message : '获取K线数据失败',
            candlestickLoading: false 
          })
        }
      },
      
      setCandlestickTimeframe: (timeframe) => {
        set({ candlestickTimeframe: timeframe })
        // 自动重新获取数据
        get().fetchCandlestickData({ timeframe })
      },
      
      setCandlestickDataType: (dataType) => {
        set({ candlestickDataType: dataType })
        // 自动重新获取数据
        get().fetchCandlestickData({ dataType })
      },
      
      toggleTechnicalIndicators: (show) => {
        const newShow = show !== undefined ? show : !get().showTechnicalIndicators
        set({ showTechnicalIndicators: newShow })
        // 自动重新获取数据
        get().fetchCandlestickData({ includeTechnicalIndicators: newShow })
      },
      
      setSelectedTechnicalIndicators: (indicators) => {
        set({ selectedTechnicalIndicators: indicators })
        // 如果当前显示技术指标，则重新获取数据
        if (get().showTechnicalIndicators) {
          get().fetchCandlestickData({ technicalIndicators: indicators })
        }
      },
      
      setChartZoomRange: (range) => set({ chartZoomRange: range }),
      
      setSelectedDataPoint: (dataPoint) => set({ selectedDataPoint: dataPoint }),
      
      clearCandlestickError: () => set({ candlestickError: null }),
      
      resetCandlestickState: () => set({
        candlestickData: null,
        candlestickError: null,
        candlestickTimeframe: 'day',
        candlestickDataType: 'revenue',
        showTechnicalIndicators: false,
        selectedTechnicalIndicators: ['MA5', 'MA20'],
        chartZoomRange: null,
        selectedDataPoint: null
      }),
      
      // ==================== 通用操作实现 ====================
      
      clearAllErrors: () => set({ 
        statsError: null, 
        candlestickError: null 
      }),
      
      resetAllState: () => set({
        statsData: null,
        statsLoading: false,
        statsError: null,
        timeRange: { startTime: '', endTime: '' },
        includeAnchor: true,
        candlestickData: null,
        candlestickLoading: false,
        candlestickError: null,
        candlestickTimeframe: 'day',
        candlestickDataType: 'revenue',
        showTechnicalIndicators: false,
        selectedTechnicalIndicators: ['MA5', 'MA20'],
        chartZoomRange: null,
        selectedDataPoint: null
      })
    }),
    { 
      name: 'financial-store',
      // 开发环境下启用Redux DevTools
      enabled: process.env.NODE_ENV === 'development'
    }
  )
)
