/**
 * K线图相关类型定义
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

// ==================== 基础数据类型 ====================

/**
 * K线数据点
 */
export interface CandlestickDataPoint {
  /** 日期 */
  date: string
  /** 开盘价 */
  open: number
  /** 收盘价 */
  close: number
  /** 最低价 */
  low: number
  /** 最高价 */
  high: number
  /** 成交量 */
  volume: number
}

/**
 * 技术指标数据
 */
export interface TechnicalIndicator {
  /** 指标名称 */
  name: string
  /** 指标数据 */
  data: number[]
  /** 显示颜色 */
  color: string
  /** 图表类型 */
  type: 'line' | 'area'
  /** 是否显示 */
  visible?: boolean
}

/**
 * K线统计信息
 */
export interface CandlestickStatistics {
  /** 总成交量 */
  totalVolume: number
  /** 平均成交量 */
  avgVolume: number
  /** 最高价 */
  maxHigh: number
  /** 最低价 */
  minLow: number
  /** 价格变化 */
  priceChange: number
  /** 价格变化百分比 */
  priceChangePercent: number
}

// ==================== 请求响应类型 ====================

/**
 * K线图数据查询请求
 */
export interface CandlestickRequest {
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 时间粒度 */
  timeframe: 'day' | 'week' | 'month'
  /** 数据类型 */
  dataType: 'revenue' | 'profit' | 'commission'
  /** 是否包含技术指标 */
  includeTechnicalIndicators?: boolean
}

/**
 * K线图完整数据响应
 */
export interface CandlestickDataResponse {
  /** K线数据 */
  candleData: CandlestickDataPoint[]
  /** 技术指标数据 */
  technicalIndicators: Record<string, number[]>
  /** 统计信息 */
  statistics: CandlestickStatistics
}

// ==================== 组件Props类型 ====================

/**
 * K线图组件Props
 */
export interface CandlestickChartProps {
  /** K线数据 */
  data: CandlestickDataPoint[]
  /** 加载状态 */
  loading?: boolean
  /** 样式类名 */
  className?: string
  /** 图表标题 */
  title?: string
  /** 图表高度 */
  height?: number
  /** 是否显示成交量 */
  showVolume?: boolean
  /** 是否显示技术指标 */
  showTechnicalIndicators?: boolean
  /** 技术指标列表 */
  technicalIndicators?: TechnicalIndicator[]
  /** 时间周期 */
  timeframe?: 'day' | 'week' | 'month'
  /** 时间周期变化回调 */
  onTimeframeChange?: (timeframe: 'day' | 'week' | 'month') => void
  /** 数据类型 */
  dataType?: 'revenue' | 'profit' | 'commission'
  /** 数据类型变化回调 */
  onDataTypeChange?: (dataType: 'revenue' | 'profit' | 'commission') => void
}

/**
 * 技术指标组件Props
 */
export interface TechnicalIndicatorsProps {
  /** 是否启用技术指标 */
  enabled?: boolean
  /** 选中的技术指标 */
  selectedIndicators: string[]
  /** 技术指标开关回调 */
  onToggle?: (enabled: boolean) => void
  /** 技术指标变化回调 */
  onIndicatorChange?: (indicators: string[]) => void
  /** 样式类名 */
  className?: string
}

// ==================== 工具类型 ====================

/**
 * 移动平均线类型
 */
export type MovingAverageType = 'SMA' | 'EMA' | 'WMA'

/**
 * 技术指标配置
 */
export interface TechnicalIndicatorConfig {
  /** 指标类型 */
  type: 'MA' | 'BOLL' | 'RSI' | 'MACD' | 'KDJ'
  /** 参数配置 */
  params: Record<string, number>
  /** 显示配置 */
  display: {
    color: string
    lineWidth: number
    lineType: 'solid' | 'dashed' | 'dotted'
  }
}

/**
 * K线图主题配置
 */
export interface CandlestickTheme {
  /** 上涨颜色 */
  upColor: string
  /** 下跌颜色 */
  downColor: string
  /** 背景颜色 */
  backgroundColor: string
  /** 网格颜色 */
  gridColor: string
  /** 文字颜色 */
  textColor: string
}

/**
 * 数据转换选项
 */
export interface DataTransformOptions {
  /** 时间格式 */
  timeFormat: string
  /** 数值精度 */
  precision: number
  /** 是否填充缺失数据 */
  fillMissingData: boolean
}

// ==================== 常量定义 ====================

/**
 * 默认技术指标配置
 */
export const DEFAULT_TECHNICAL_INDICATORS: Record<string, TechnicalIndicatorConfig> = {
  MA5: {
    type: 'MA',
    params: { period: 5 },
    display: { color: '#3b82f6', lineWidth: 2, lineType: 'solid' }
  },
  MA20: {
    type: 'MA',
    params: { period: 20 },
    display: { color: '#f59e0b', lineWidth: 2, lineType: 'solid' }
  },
  BOLL: {
    type: 'BOLL',
    params: { period: 20, stdDev: 2 },
    display: { color: '#8b5cf6', lineWidth: 1, lineType: 'dashed' }
  },
  RSI: {
    type: 'RSI',
    params: { period: 14 },
    display: { color: '#ef4444', lineWidth: 2, lineType: 'solid' }
  }
}

/**
 * 默认K线图主题
 */
export const DEFAULT_CANDLESTICK_THEME: CandlestickTheme = {
  upColor: '#10b981',
  downColor: '#ef4444',
  backgroundColor: 'transparent',
  gridColor: '#f3f4f6',
  textColor: '#374151'
}

/**
 * 时间周期选项
 */
export const TIMEFRAME_OPTIONS = [
  { value: 'day', label: '日K', shortLabel: '日' },
  { value: 'week', label: '周K', shortLabel: '周' },
  { value: 'month', label: '月K', shortLabel: '月' }
] as const

/**
 * 数据类型选项
 */
export const DATA_TYPE_OPTIONS = [
  { value: 'revenue', label: '收入数据', description: '基于充值订单的收入K线' },
  { value: 'profit', label: '利润数据', description: '收入减去佣金支出的净利润K线' },
  { value: 'commission', label: '佣金数据', description: '基于佣金结算订单的支出K线' }
] as const
