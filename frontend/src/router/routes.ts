import { lazy } from 'react'
import type { AppRoute, Menu } from '@/types'
import { PERMISSIONS } from '@/constants'
import { isNavigationMenuType, getMenuTypeLabel } from '../constants/menu'

// 懒加载页面组件
const Login = lazy(() => import('../pages/auth/Login.tsx'))
const MainLayout = lazy(() => import('../components/layout/MainLayout.tsx').then(m => ({ default: m.MainLayout })))
const Dashboard = lazy(() => import('../pages/dashboard/Dashboard.tsx'))
const UserList = lazy(() => import('../pages/system/user/UserList.tsx'))
const UserSync = lazy(() => import('../pages/system/user/UserSync.tsx'))
const RoleList = lazy(() => import('../pages/system/role/RoleList.tsx'))
const DeptList = lazy(() => import('../pages/system/dept/DeptList.tsx'))
const TenantList = lazy(() => import('../pages/system/tenant/TenantList.tsx'))
const MenuList = lazy(() => import('../pages/system/menu/MenuList.tsx'))
const FinancialDashboard = lazy(() => import('../pages/financial/FinancialDashboard.tsx'))
const OrderManagement = lazy(() => import('../pages/financial/OrderManagement.tsx').then(m => ({ default: m.OrderManagement })))
const CandlestickAnalysis = lazy(() => import('../pages/financial/CandlestickAnalysis.tsx').then(m => ({ default: m.CandlestickAnalysis })))
const YunyingPage = lazy(() => import('../pages/yunying/index.tsx'))
const AnchorProfile = lazy(() => import('../pages/anchor/AnchorProfile.tsx'))
const SubUserList = lazy(() => import('../pages/operations/SubUserList.tsx'))
const RechargeDetails = lazy(() => import('../pages/operations/RechargeDetails.tsx'))
const ConsumeDetails = lazy(() => import('../pages/operations/ConsumeDetails.tsx'))
// 监控管理模块
const OperLogList = lazy(() => import('../pages/monitor/operlog/OperLogList.tsx'))
const SystemInfo = lazy(() => import('../pages/monitor/system/SystemInfo.tsx'))
const NotFound = lazy(() => import('../pages/common/NotFound.tsx'))

/**
 * 路由配置
 */
export const routes: AppRoute[] = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      hidden: true,
    },
  },
  {
    path: '/',
    name: 'Layout',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'DashboardRedirect',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: '仪表板',
          icon: 'LayoutDashboard',
          permissions: [PERMISSIONS.DASHBOARD.VIEW],
        },
      },
      {
        path: 'system',
        name: 'System',
        meta: {
          title: '系统管理',
          icon: 'Settings',
          permissions: [PERMISSIONS.SYSTEM.VIEW],
        },
        children: [
          {
            path: '',
            name: 'SystemRedirect',
            redirect: '/system/user',
          },
          {
            path: 'user',
            name: 'SystemUser',
            component: UserList,
            meta: {
              title: '用户管理',
              icon: 'Users',
              permissions: [PERMISSIONS.SYSTEM.USER.LIST],
              keepAlive: true,
            },
          },
          {
            path: 'user/sync',
            name: 'UserSync',
            component: UserSync,
            meta: {
              title: '用户同步',
              icon: 'RefreshCw',
              permissions: [PERMISSIONS.SYSTEM.USER.SYNC],
              hidden: true, // 不在菜单中显示，通过按钮跳转
            },
          },
          {
            path: 'role',
            name: 'AuthRole',
            component: RoleList,
            meta: {
              title: '角色管理',
              icon: 'UserCheck',
              // permissions: [PERMISSIONS.SYSTEM.ROLE.LIST],
              keepAlive: true,
            },
          },
          {
            path: 'dept',
            name: 'SystemDept',
            component: DeptList,
            meta: {
              title: '部门管理',
              icon: 'Building2',
              permissions: [PERMISSIONS.SYSTEM.DEPT.LIST],
              keepAlive: true,
            },
          },
          {
            path: 'tenant',
            name: 'SystemTenant',
            component: TenantList,
            meta: {
              title: '租户管理',
              icon: 'Building',
              permissions: [PERMISSIONS.SYSTEM.TENANT.LIST],
              keepAlive: true,
            },
          },
          {
            path: 'menu',
            name: 'AuthMenu',
            component: MenuList,
            meta: {
              title: '菜单管理',
              icon: 'Menu',
              permissions: [PERMISSIONS.MENU.LIST],
              keepAlive: true,
            },
          },
        ],
      },
      {
        path: 'financial',
        name: 'Financial',
        meta: {
          title: '财务数据管理',
          icon: 'TrendingUp',
          permissions: [PERMISSIONS.FINANCIAL.VIEW],
        },
        children: [
          {
            path: '',
            name: 'FinancialRedirect',
            redirect: '/financial/stats',
          },
          {
            path: 'stats',
            name: 'FinancialDashboard',
            component: FinancialDashboard,
            meta: {
              title: '财务数据统计',
              icon: 'BarChart3',
              permissions: [PERMISSIONS.FINANCIAL.STATS.VIEW],
              keepAlive: true,
            },
          },
          {
            path: 'orders',
            name: 'OrderManagement',
            component: OrderManagement,
            meta: {
              title: '佣金结算订单',
              icon: 'Receipt',
              permissions: [PERMISSIONS.COMMISSION.ORDER.LIST],
              keepAlive: true,
            },
          },
          {
            path: 'candlestick',
            name: 'CandlestickAnalysis',
            component: CandlestickAnalysis,
            meta: {
              title: 'K线图分析',
              icon: 'TrendingUp',
              permissions: [PERMISSIONS.FINANCIAL.STATS.VIEW],
              keepAlive: true,
            },
          },
          {
            path: 'candlestick',
            name: 'CandlestickAnalysis',
            component: CandlestickAnalysis,
            meta: {
              title: 'K线图分析',
              icon: 'TrendingUp',
              permissions: [PERMISSIONS.FINANCIAL.STATS.VIEW],
              keepAlive: true,
            },
          },
        ],
      },
      {
        path: 'yunying',
        name: 'Yunying',
        meta: {
          title: '运营管理',
          icon: 'Users',
          permissions: [PERMISSIONS.OPERATIONS.VIEW],
        },
        children: [
          {
            path: '',
            name: 'YunyingRedirect',
            redirect: '/yunying/anchors',
          },
          {
            path: 'anchors',
            name: 'YunyingAnchors',
            component:YunyingPage,
            meta: {
              title: '主播管理',
              icon: 'UserCheck',
              permissions: [PERMISSIONS.OPERATIONS.ANCHOR.LIST],
              keepAlive: true,
            },
          },
          {
            path: 'profile',
            name: 'AnchorProfile',
            component: AnchorProfile,
            meta: {
              title: '我的信息',
              icon: 'User',
              permissions: [PERMISSIONS.OPERATIONS.ANCHOR.VIEW],
              hidden: true, // 主播专用页面，不在菜单中显示
            },
          },
          {
            path: 'sub-users',
            name: 'SubUserList',
            component: SubUserList,
            meta: {
              title: '下级用户列表',
              icon: 'Users',
              permissions: [PERMISSIONS.OPERATIONS.ANCHOR.SUB_USERS],
              hidden: true, // 通过参数访问，不在菜单中显示
            },
          },
          {
            path: 'recharge-details',
            name: 'RechargeDetails',
            component: RechargeDetails,
            meta: {
              title: '充值明细',
              icon: 'CreditCard',
              permissions: [PERMISSIONS.OPERATIONS.USER.RECHARGE],
              hidden: true, // 通过参数访问，不在菜单中显示
            },
          },
          {
            path: 'consume-details',
            name: 'ConsumeDetails',
            component: ConsumeDetails,
            meta: {
              title: '消费明细',
              icon: 'ShoppingCart',
              permissions: [PERMISSIONS.OPERATIONS.USER.CONSUME],
              hidden: true, // 通过参数访问，不在菜单中显示
            },
          },
        ],
      },
      {
        path: 'monitor',
        name: 'Monitor',
        meta: {
          title: '监控管理',
          icon: 'Activity',
          permissions: [PERMISSIONS.MONITOR.VIEW],
        },
        children: [
          {
            path: '',
            name: 'MonitorRedirect',
            redirect: '/monitor/operlog',
          },
          {
            path: 'operlog',
            name: 'OperLogList',
            component: OperLogList,
            meta: {
              title: '操作日志',
              icon: 'FileText',
              permissions: [PERMISSIONS.MONITOR.OPERLOG.LIST],
              keepAlive: true,
            },
          },
          // {
          //   path: 'loginlog',
          //   name: 'LoginLogList',
          //   component: LoginLogList,
          //   meta: {
          //     title: '登录日志',
          //     icon: 'LogIn',
          //     permissions: [PERMISSIONS.MONITOR.LOGINLOG.LIST],
          //     keepAlive: true,
          //   },
          // },
          {
            path: 'system',
            name: 'SystemInfo',
            component: SystemInfo,
            meta: {
              title: '系统信息',
              icon: 'Server',
              permissions: [PERMISSIONS.MONITOR.SERVER.LIST],
              keepAlive: true,
            },
          },
        ],
      },
      {
        path: '/404',
        name: 'NotFound',
        component: NotFound,
        meta: {
          title: '页面不存在',
          hidden: true,
        },
      },
    ],
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面不存在',
      hidden: true,
    },
  },
  {
    path: '*',
    name: 'NotFoundRedirect',
    redirect: '/404',
  },
]

/**
 * 根据权限过滤路由 - 支持通配符权限和超级管理员
 */
export const filterRoutesByPermissions = (
  routes: AppRoute[],
  permissions: string[],
  roles: string[] = []
): AppRoute[] => {
  // 检查是否为超级管理员
  const isSuperAdmin = permissions.includes('*:*:*') || roles.includes('SUPER_ADMIN')

  // 权限检查函数，支持通配符
  const hasPermission = (permission: string): boolean => {
    // 超级管理员拥有所有权限
    if (isSuperAdmin) {
      return true
    }

    // 精确匹配
    if (permissions.includes(permission)) {
      return true
    }

    // 通配符匹配
    const parts = permission.split(':')
    for (const perm of permissions) {
      if (perm.includes('*')) {
        const permParts = perm.split(':')
        let match = true
        for (let i = 0; i < Math.max(parts.length, permParts.length); i++) {
          const permPart = permParts[i] || ''
          const reqPart = parts[i] || ''
          if (permPart !== '*' && permPart !== reqPart) {
            match = false
            break
          }
        }
        if (match) {
          return true
        }
      }
    }

    return false
  }

  return routes
    .map(route => {
      // 递归处理子路由
      if (route.children) {
        const filteredChildren = filterRoutesByPermissions(route.children, permissions, roles)
        return {
          ...route,
          children: filteredChildren
        }
      }
      return route
    })
    .filter(route => {
      // 如果路由没有权限要求，直接显示
      if (!route.meta?.permissions) {
        return true
      }

      // 检查是否有任一权限
      const hasRoutePermission = route.meta.permissions.some(permission =>
        hasPermission(permission)
      )

      // 如果当前路由没有权限，但有子路由，检查子路由是否有权限
      if (!hasRoutePermission && route.children && route.children.length > 0) {
        return true // 保留父路由，让子路由过滤决定
      }

      return hasRoutePermission
    })
}

/**
 * 根据角色过滤路由
 */
export const filterRoutesByRoles = (
  routes: AppRoute[],
  roles: string[]
): AppRoute[] => {
  return routes
    .map(route => {
      // 递归处理子路由
      if (route.children) {
        const filteredChildren = filterRoutesByRoles(route.children, roles)
        return {
          ...route,
          children: filteredChildren
        }
      }
      return route
    })
    .filter(route => {
      // 如果路由没有角色要求，直接显示
      if (!route.meta?.roles) {
        return true
      }

      // 检查是否有任一角色
      const hasRole = route.meta.roles.some(role =>
        roles.includes(role)
      )

      // 如果当前路由没有角色，但有子路由，检查子路由是否有角色
      if (!hasRole && route.children && route.children.length > 0) {
        return true // 保留父路由，让子路由过滤决定
      }

      return hasRole
    })
}

/**
 * 递归过滤菜单路由
 */
const filterMenuRoutes = (routes: AppRoute[]): AppRoute[] => {
  return routes
    .map(route => {
      // 递归处理子路由
      if (route.children) {
        const filteredChildren = filterMenuRoutes(route.children)
        return {
          ...route,
          children: filteredChildren.length > 0 ? filteredChildren : undefined
        }
      }
      return route
    })
    .filter(route => {
      // 过滤隐藏的路由
      if (route.meta?.hidden) {
        return false
      }

      // 过滤重定向路由
      if (route.redirect) {
        return false
      }

      // 过滤空路径且没有标题的路由
      if (route.path === '' && !route.meta?.title) {
        return false
      }

      // 如果有标题，说明是菜单项，保留
      if (route.meta?.title) {
        return true
      }

      // 如果有子路由，保留（作为菜单分组）
      if (route.children && route.children.length > 0) {
        return true
      }

      // 其他情况过滤掉
      return false
    })
}

/**
 * 基于后端菜单数据过滤路由
 */
export const filterRoutesByBackendMenus = (
  routes: AppRoute[],
  backendMenus: Menu[]
): AppRoute[] => {
  // 创建后端菜单路径集合，用于快速查找
  const menuPaths = new Set<string>()
  const menuPermissions = new Set<string>()

  // 递归收集所有菜单路径和权限
  const collectMenuData = (menus: Menu[]) => {
    menus.forEach(menu => {
      if (menu.path) {
        menuPaths.add(menu.path)
      }
      if (menu.permission) {
        menuPermissions.add(menu.permission)
      }
      if (menu.children && menu.children.length > 0) {
        collectMenuData(menu.children)
      }
    })
  }

  collectMenuData(backendMenus)

  // 递归过滤路由
  const filterRoutes = (routeList: AppRoute[]): AppRoute[] => {
    return routeList
      .map(route => {
        // 递归处理子路由
        if (route.children) {
          const filteredChildren = filterRoutes(route.children)
          return {
            ...route,
            children: filteredChildren.length > 0 ? filteredChildren : undefined
          }
        }
        return route
      })
      .filter(route => {
        // 过滤隐藏的路由
        if (route.meta?.hidden) {
          return false
        }

        // 过滤重定向路由
        if (route.redirect) {
          return false
        }

        // 检查路径匹配
        const pathMatch = menuPaths.has(route.path)

        // 检查权限匹配
        const permissionMatch = route.meta?.permissions?.some(permission =>
          menuPermissions.has(permission)
        ) ?? false

        // 如果有子路由，保留父路由
        if (route.children && route.children.length > 0) {
          return true
        }

        // 路径匹配或权限匹配的路由保留
        return pathMatch || permissionMatch || !route.meta?.permissions
      })
  }

  return filterRoutes(routes)
}

/**
 * 将后端菜单转换为前端路由格式
 */
const convertBackendMenusToRoutes = (backendMenus: Menu[]): AppRoute[] => {
  console.log('convertBackendMenusToRoutes 输入:', backendMenus)

  const routes = backendMenus
    .filter(menu => {
      // 只保留目录和菜单类型，过滤掉按钮类型
      const isNavMenu = isNavigationMenuType(menu.type)
      console.log('菜单类型过滤:', {
        id: menu.id,
        name: menu.name,
        type: menu.type,
        typeLabel: getMenuTypeLabel(menu.type),
        isNavigationMenu: isNavMenu,
        visible: menu.visible,
        enabled: menu.enabled
      })
      return isNavMenu && menu.visible && menu.enabled
    })
    .map(menu => {
      console.log('处理导航菜单:', {
        id: menu.id,
        name: menu.name,
        title: menu.title,
        path: menu.path,
        type: menu.type,
        children: menu.children?.length || 0
      })

      const route: AppRoute = {
        path: menu.path || `/${menu.name}`,
        name: menu.name,
        meta: {
          title: menu.title,
          icon: menu.icon,
          permissions: menu.permission ? [menu.permission] : undefined,
          hidden: !menu.visible,
        },
        children: menu.children ? convertBackendMenusToRoutes(menu.children) : undefined
      }

      console.log('生成的导航路由:', {
        path: route.path,
        name: route.name,
        title: route.meta?.title,
        hasChildren: !!route.children?.length
      })
      return route
    })
    .filter(route => {
      const hasTitle = !!route.meta?.title
      console.log('最终路由验证:', { route: route.name, hasTitle, title: route.meta?.title })
      return hasTitle
    })

  console.log('convertBackendMenusToRoutes 输出 (仅导航菜单):', routes)
  return routes
}

/**
 * 基于后端菜单生成前端菜单数据
 */
export const generateMenusFromBackend = (
  routes: AppRoute[],
  backendMenus: Menu[]
): AppRoute[] => {
  console.log('generateMenusFromBackend 调试信息:', {
    backendMenus: backendMenus,
    backendMenusLength: backendMenus?.length || 0,
    hasBackendMenus: backendMenus && backendMenus.length > 0
  })

  // 如果有后端菜单数据，直接转换为前端路由格式
  if (backendMenus && backendMenus.length > 0) {
    const convertedRoutes = convertBackendMenusToRoutes(backendMenus)
    console.log('转换后的路由:', convertedRoutes)
    return convertedRoutes
  }

  // 否则使用原有的路由过滤逻辑
  const filteredRoutes = filterRoutesByBackendMenus(routes, backendMenus)
  return filterMenuRoutes(filteredRoutes)
}

/**
 * 根据路径查找路由
 */
export const findRouteByPath = (routes: AppRoute[], path: string): AppRoute | null => {
  for (const route of routes) {
    if (route.path === path) {
      return route
    }
    
    if (route.children) {
      const found = findRouteByPath(route.children, path)
      if (found) {
        return found
      }
    }
  }
  
  return null
}

/**
 * 生成面包屑导航
 */
export const generateBreadcrumbs = (
  routes: AppRoute[],
  currentPath: string,
  searchParams?: URLSearchParams
): Array<{ title: string; path?: string }> => {
  const breadcrumbs: Array<{ title: string; path?: string }> = []

  // 构建完整路径的函数
  const buildFullPath = (parents: AppRoute[]): string => {
    const pathSegments: string[] = []

    for (const parent of parents) {
      if (parent.path && parent.path !== '' && parent.path !== '/') {
        // 移除路径开头和结尾的斜杠，然后添加到segments中
        const cleanPath = parent.path.replace(/^\/+|\/+$/g, '')
        if (cleanPath) {
          pathSegments.push(cleanPath)
        }
      }
    }

    if (pathSegments.length === 0) {
      return '/'
    }

    return '/' + pathSegments.join('/')
  }

  const findPath = (routeList: AppRoute[], targetPath: string, parents: AppRoute[] = []): boolean => {
    for (const route of routeList) {
      const currentParents = [...parents, route]
      const fullPath = buildFullPath(currentParents)

      console.log('🔍 检查路径匹配:', fullPath, 'vs', targetPath)

      if (fullPath === targetPath) {
        console.log('✅ 找到匹配路径:', fullPath)
        console.log('📋 当前父级路由:', currentParents.map(p => ({ path: p.path, title: p.meta?.title, hidden: p.meta?.hidden })))

        // 找到目标路由，生成面包屑
        currentParents.forEach((parent, index) => {
          console.log(`🔍 检查父级路由 ${index}:`, {
            path: parent.path,
            title: parent.meta?.title,
            hidden: parent.meta?.hidden,
            hasTitle: !!parent.meta?.title,
            notHidden: !parent.meta?.hidden
          })

          if (parent.meta?.title && !parent.meta?.hidden) {
            let title = parent.meta.title

            // 对于最后一个面包屑项目，根据URL参数动态生成标题
            if (index === currentParents.length - 1 && searchParams) {
              title = generateDynamicTitle(parent.meta.title, targetPath, searchParams)
            }

            // 构建该层级的完整路径
            const parentPath = buildFullPath(currentParents.slice(0, index + 1))

            console.log('➕ 添加面包屑项:', { title, path: parentPath })

            breadcrumbs.push({
              title,
              path: parentPath,
            })
          } else {
            console.log('❌ 跳过面包屑项:', {
              reason: !parent.meta?.title ? '没有标题' : '被隐藏',
              path: parent.path
            })
          }
        })
        return true
      }

      if (route.children) {
        if (findPath(route.children, targetPath, currentParents)) {
          return true
        }
      }
    }

    return false
  }

  findPath(routes, currentPath)
  console.log('🍞 最终生成的面包屑:', breadcrumbs)
  return breadcrumbs
}

/**
 * 根据路径和参数生成动态标题
 */
const generateDynamicTitle = (
  originalTitle: string,
  path: string,
  searchParams: URLSearchParams
): string => {
  switch (path) {
    case '/yunying/sub-users': {
      const anchorName = searchParams.get('anchorName')
      return anchorName ? `${anchorName} - 下级用户列表` : originalTitle
    }

    case '/yunying/recharge-details': {
      const queryType = searchParams.get('type') || 'user'
      const displayName = queryType === 'anchor'
        ? searchParams.get('anchorName') || '未知主播'
        : searchParams.get('userName') || '未知用户'
      const displayTitle = queryType === 'anchor' ? '主播充值明细' : '用户充值明细'
      return `${displayName} - ${displayTitle}`
    }

    case '/yunying/consume-details': {
      const queryType = searchParams.get('type') || 'user'
      const displayName = queryType === 'anchor'
        ? searchParams.get('anchorName') || '未知主播'
        : searchParams.get('userName') || '未知用户'
      const displayTitle = queryType === 'anchor' ? '主播消费明细' : '用户消费明细'
      return `${displayName} - ${displayTitle}`
    }

    default:
      return originalTitle
  }
}
