import React from 'react'
import { FileText, User, CreditCard, Calendar, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { OrderService } from '@/services/financial'
import type { OrderVO } from '@/types/financial'

interface OrderDetailDialogProps {
  open: boolean
  onClose: () => void
  order: OrderVO | null
}

/**
 * 订单详情对话框
 */
export const OrderDetailDialog: React.FC<OrderDetailDialogProps> = ({
  open,
  onClose,
  order
}) => {
  if (!order) {
    return null
  }

  /**
   * 获取状态徽章样式
   */
  const getStatusBadgeVariant = (status: number) => {
    switch (status) {
      case 0: return 'secondary' // 已创建
      case 1: return 'default'   // 已计算
      case 2: return 'warning'   // 待结算
      case 3: return 'success'   // 已结算
      case 4: return 'destructive' // 已取消
      default: return 'secondary'
    }
  }

  /**
   * 获取结算状态徽章样式
   */
  const getSettlementBadgeVariant = (status: number) => {
    switch (status) {
      case 0: return 'secondary' // 无需结算
      case 1: return 'warning'   // 待处理
      case 2: return 'success'   // 结算成功
      case 3: return 'destructive' // 结算失败
      default: return 'secondary'
    }
  }

  /**
   * 获取结算金额信息
   */
  const getSettlementAmountInfo = () => {
    const isSettled = order.settlementStatus === 2 // 结算成功
    const hasActualAmount = order.actualSettlementAmount && order.actualSettlementAmount > 0

    if (isSettled && hasActualAmount) {
      return {
        label: '实际结算金额',
        amount: order.actualSettlementAmount,
        color: 'text-green-600',
        description: '用户输入的实际结算金额'
      }
    } else if (isSettled) {
      return {
        label: '系统结算金额',
        amount: order.feeActual,
        color: 'text-blue-600',
        description: '系统计算的结算金额'
      }
    } else {
      return {
        label: '预计佣金',
        amount: order.feeValue,
        color: 'text-gray-600',
        description: '系统计算的预计佣金'
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            订单详情
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">基本信息</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">订单ID</label>
                <p className="mt-1 text-sm text-gray-900 font-mono">{order.payid}</p>
              </div>
              
              {order.parentsPayid && (
                <div>
                  <label className="text-sm font-medium text-gray-500">父订单ID</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono">{order.parentsPayid}</p>
                </div>
              )}
              
              <div>
                <label className="text-sm font-medium text-gray-500">订单类型</label>
                <div className="mt-1">
                  {order.isMainOrder ? (
                    <Badge variant="default">主订单</Badge>
                  ) : (
                    <Badge variant="secondary">子订单</Badge>
                  )}
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">子订单数量</label>
                <p className="mt-1 text-sm text-gray-900">
                  {order.subOrders ? order.subOrders.length : 0} 个
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* 用户信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
              <User className="w-5 h-5" />
              用户信息
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              {order.agent && (
                <>
                  <div>
                    <label className="text-sm font-medium text-gray-500">代理ID</label>
                    <p className="mt-1 text-sm text-gray-900">{order.agent}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">代理昵称</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.agentNickname || '未设置'}
                    </p>
                  </div>
                </>
              )}
              
              {order.anchor && (
                <>
                  <div>
                    <label className="text-sm font-medium text-gray-500">主播ID</label>
                    <p className="mt-1 text-sm text-gray-900">{order.anchor}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">主播昵称</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.anchorNickname || '未设置'}
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          <Separator />

          {/* 金额信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              金额信息
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">自充值金额</label>
                <p className="mt-1 text-lg font-semibold text-gray-900">
                  {OrderService.formatAmount(order.amountSelf)}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">推广充值金额</label>
                <p className="mt-1 text-lg font-semibold text-gray-900">
                  {OrderService.formatAmount(order.amountFans)}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">总充值金额</label>
                <p className="mt-1 text-xl font-bold text-blue-600">
                  {OrderService.formatAmount(order.totalAmount)}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">劳务比例</label>
                <p className="mt-1 text-lg font-semibold text-gray-900">
                  {order.feeRate}%
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">推广劳务费</label>
                <p className="mt-1 text-lg font-semibold text-gray-600">
                  {OrderService.formatAmount(order.feeValue)}
                </p>
                <p className="text-xs text-gray-500 mt-1">系统计算的推广劳务费</p>
              </div>

              <div>
                {(() => {
                  const amountInfo = getSettlementAmountInfo()
                  return (
                    <>
                      <label className="text-sm font-medium text-gray-500">{amountInfo.label}</label>
                      <p className={`mt-1 text-xl font-bold ${amountInfo.color}`}>
                        {OrderService.formatAmount(amountInfo.amount)}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">{amountInfo.description}</p>
                    </>
                  )
                })()}
              </div>
            </div>
          </div>

          <Separator />

          {/* 收款信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              收款信息
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">收款人</label>
                <p className="mt-1 text-sm text-gray-900">{order.feePerson}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">收款账号</label>
                <p className="mt-1 text-sm text-gray-900 font-mono">{order.feeAccount}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* 状态信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">状态信息</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">订单状态</label>
                <div className="mt-1">
                  <Badge variant={getStatusBadgeVariant(order.orderStatus)}>
                    {order.orderStatusDesc}
                  </Badge>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">结算状态</label>
                <div className="mt-1">
                  <Badge variant={getSettlementBadgeVariant(order.settlementStatus)}>
                    {order.settlementStatusDesc}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* 时间信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              时间信息
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">创建时间</label>
                <p className="mt-1 text-sm text-gray-900">
                  {OrderService.formatTimestamp(order.createTime)}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">更新时间</label>
                <p className="mt-1 text-sm text-gray-900">
                  {OrderService.formatTimestamp(order.updateTime)}
                </p>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={onClose}>
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
