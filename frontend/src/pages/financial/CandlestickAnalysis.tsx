import React, { useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  RefreshCw,
  AlertCircle,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import { CandlestickChart } from '@/components/charts/CandlestickChart'
import { TechnicalIndicators } from '@/components/charts/TechnicalIndicators'
import { TimeRangeSelector } from '@/components/charts/TimeRangeSelector'
import { DataTypeSelector } from '@/components/charts/DataTypeSelector'
import { useFinancialStore } from '@/stores'
import { calculateAllTechnicalIndicators } from '@/utils/technicalIndicators'
import type { TechnicalIndicator } from '@/types/candlestick'

/**
 * K线图分析页面
 * 提供完整的K线图分析功能，包括数据展示、技术指标、时间范围选择等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
export const CandlestickAnalysis: React.FC = () => {
  const {
    // K线图数据状态
    candlestickData,
    candlestickLoading,
    candlestickError,
    
    // K线图配置
    candlestickTimeframe,
    candlestickDataType,
    showTechnicalIndicators,
    selectedTechnicalIndicators,
    
    // 时间范围
    timeRange,
    
    // 操作方法
    fetchCandlestickData,
    setCandlestickTimeframe,
    setCandlestickDataType,
    toggleTechnicalIndicators,
    setSelectedTechnicalIndicators,
    setTimeRange,
    clearCandlestickError
  } = useFinancialStore()

  // 初始化数据
  useEffect(() => {
    // 如果没有时间范围，设置默认值（最近30天）
    if (!timeRange.startTime || !timeRange.endTime) {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      
      const formatDateTime = (date: Date): string => {
        return date.toISOString().slice(0, 19).replace('T', ' ')
      }
      
      setTimeRange({
        startTime: formatDateTime(start),
        endTime: formatDateTime(end)
      })
    } else {
      // 有时间范围则直接获取数据
      fetchCandlestickData()
    }
  }, [])

  // 处理时间范围变化
  const handleTimeRangeChange = (startTime: string, endTime: string) => {
    setTimeRange({ startTime, endTime })
    fetchCandlestickData({ startTime, endTime })
  }

  // 处理数据类型变化
  const handleDataTypeChange = (dataType: 'revenue' | 'profit' | 'commission') => {
    setCandlestickDataType(dataType)
  }

  // 处理时间粒度变化
  const handleTimeframeChange = (timeframe: 'day' | 'week' | 'month') => {
    setCandlestickTimeframe(timeframe)
  }

  // 处理技术指标开关
  const handleTechnicalIndicatorsToggle = (enabled: boolean) => {
    toggleTechnicalIndicators(enabled)
  }

  // 处理技术指标选择变化
  const handleTechnicalIndicatorsChange = (indicators: string[]) => {
    setSelectedTechnicalIndicators(indicators)
  }

  // 手动刷新数据
  const handleRefresh = () => {
    fetchCandlestickData()
  }

  // 计算技术指标数据
  const technicalIndicatorsData = useMemo((): TechnicalIndicator[] => {
    if (!candlestickData?.candleData || !showTechnicalIndicators || selectedTechnicalIndicators.length === 0) {
      return []
    }

    const indicators = calculateAllTechnicalIndicators(
      candlestickData.candleData.map((item: { close: any }) => item.close),
      selectedTechnicalIndicators
    )

    const colors = {
      MA5: '#3b82f6',
      MA10: '#8b5cf6',
      MA20: '#f59e0b',
      MA60: '#ef4444',
      EMA12: '#06b6d4',
      EMA26: '#84cc16',
      BOLL_UPPER: '#6366f1',
      BOLL_MIDDLE: '#6366f1',
      BOLL_LOWER: '#6366f1',
      RSI: '#ec4899'
    }

    return Object.entries(indicators).map(([name, data]) => ({
      name,
      data,
      color: colors[name as keyof typeof colors] || '#6b7280',
      type: 'line' as const,
      visible: true
    }))
  }, [candlestickData, showTechnicalIndicators, selectedTechnicalIndicators])

  // 获取页面标题
  const getPageTitle = () => {
    const typeLabels = {
      revenue: '收入',
      profit: '利润',
      commission: '佣金'
    }
    
    const timeframeLabels = {
      day: '日K',
      week: '周K',
      month: '月K'
    }
    
    return `${typeLabels[candlestickDataType]}${timeframeLabels[candlestickTimeframe]}线图`
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <BarChart3 className="h-8 w-8" />
            K线图分析
          </h1>
          <p className="text-muted-foreground mt-1">
            财务数据的专业K线图分析工具，支持多种技术指标和数据类型
          </p>
        </div>
        
        <Button 
          onClick={handleRefresh} 
          disabled={candlestickLoading}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${candlestickLoading ? 'animate-spin' : ''}`} />
          刷新数据
        </Button>
      </div>

      {/* 错误提示 */}
      {candlestickError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{candlestickError}</span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearCandlestickError}
            >
              关闭
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 左侧控制面板 */}
        <div className="lg:col-span-1 space-y-6">
          {/* 时间范围选择 */}
          <TimeRangeSelector
            startTime={timeRange.startTime}
            endTime={timeRange.endTime}
            onTimeChange={handleTimeRangeChange}
            disabled={candlestickLoading}
          />

          {/* 数据类型选择 */}
          <DataTypeSelector
            selectedType={candlestickDataType}
            onTypeChange={handleDataTypeChange}
            disabled={candlestickLoading}
          />

          {/* 技术指标配置 */}
          <TechnicalIndicators
            enabled={showTechnicalIndicators}
            selectedIndicators={selectedTechnicalIndicators}
            onToggle={handleTechnicalIndicatorsToggle}
            onIndicatorChange={handleTechnicalIndicatorsChange}
          />
        </div>

        {/* 右侧图表区域 */}
        <div className="lg:col-span-3">
          {candlestickLoading ? (
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="w-full h-[500px]" />
              </CardContent>
            </Card>
          ) : (
            <CandlestickChart
              data={candlestickData?.candleData || []}
              loading={candlestickLoading}
              title={getPageTitle()}
              height={600}
              showVolume={true}
              showTechnicalIndicators={showTechnicalIndicators}
              technicalIndicators={technicalIndicatorsData}
              timeframe={candlestickTimeframe}
              onTimeframeChange={handleTimeframeChange}
              dataType={candlestickDataType}
              onDataTypeChange={handleDataTypeChange}
            />
          )}

          {/* 统计信息卡片 */}
          {candlestickData?.statistics && (
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">上涨天数</p>
                      <p className="text-lg font-semibold">{candlestickData.statistics.riseDays}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />
                    <div>
                      <p className="text-sm text-muted-foreground">下跌天数</p>
                      <p className="text-lg font-semibold">{candlestickData.statistics.fallDays}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div>
                    <p className="text-sm text-muted-foreground">上涨概率</p>
                    <p className="text-lg font-semibold text-green-600">
                      {candlestickData.statistics.riseRate?.toFixed(1)}%
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div>
                    <p className="text-sm text-muted-foreground">平均振幅</p>
                    <p className="text-lg font-semibold">
                      {candlestickData.statistics.avgAmplitude?.toFixed(2)}%
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
