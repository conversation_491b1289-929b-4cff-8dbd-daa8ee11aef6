/**
 * 技术指标计算工具函数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import type { CandlestickDataPoint } from '@/types/candlestick'

// ==================== 基础工具函数 ====================

/**
 * 从K线数据中提取收盘价
 */
export const extractClosePrices = (candleData: CandlestickDataPoint[]): number[] => {
  return candleData.map(item => item.close)
}

/**
 * 从K线数据中提取最高价
 */
export const extractHighPrices = (candleData: CandlestickDataPoint[]): number[] => {
  return candleData.map(item => item.high)
}

/**
 * 从K线数据中提取最低价
 */
export const extractLowPrices = (candleData: CandlestickDataPoint[]): number[] => {
  return candleData.map(item => item.low)
}

/**
 * 从K线数据中提取开盘价
 */
export const extractOpenPrices = (candleData: CandlestickDataPoint[]): number[] => {
  return candleData.map(item => item.open)
}

/**
 * 从K线数据中提取成交量
 */
export const extractVolumes = (candleData: CandlestickDataPoint[]): number[] => {
  return candleData.map(item => item.volume)
}

// ==================== 移动平均线 ====================

/**
 * 计算简单移动平均线 (SMA)
 * @param data 数据数组
 * @param period 周期
 * @returns SMA数组
 */
export const calculateSMA = (data: number[], period: number): number[] => {
  const result: number[] = []
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN)
    } else {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
      result.push(sum / period)
    }
  }
  
  return result
}

/**
 * 计算指数移动平均线 (EMA)
 * @param data 数据数组
 * @param period 周期
 * @returns EMA数组
 */
export const calculateEMA = (data: number[], period: number): number[] => {
  const result: number[] = []
  const multiplier = 2 / (period + 1)
  
  if (data.length === 0) return result
  
  // 第一个值使用SMA
  let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period
  result.push(...Array(period - 1).fill(NaN))
  result.push(ema)
  
  // 后续值使用EMA公式
  for (let i = period; i < data.length; i++) {
    ema = (data[i] - ema) * multiplier + ema
    result.push(ema)
  }
  
  return result
}

/**
 * 计算加权移动平均线 (WMA)
 * @param data 数据数组
 * @param period 周期
 * @returns WMA数组
 */
export const calculateWMA = (data: number[], period: number): number[] => {
  const result: number[] = []
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN)
    } else {
      let weightedSum = 0
      let weightSum = 0
      
      for (let j = 0; j < period; j++) {
        const weight = j + 1
        weightedSum += data[i - period + 1 + j] * weight
        weightSum += weight
      }
      
      result.push(weightedSum / weightSum)
    }
  }
  
  return result
}

// ==================== 布林带 ====================

/**
 * 计算布林带
 * @param data 数据数组
 * @param period 周期，默认20
 * @param stdDev 标准差倍数，默认2
 * @returns 布林带数据 {upper, middle, lower}
 */
export const calculateBollingerBands = (
  data: number[], 
  period: number = 20, 
  stdDev: number = 2
): { upper: number[], middle: number[], lower: number[] } => {
  const sma = calculateSMA(data, period)
  const upper: number[] = []
  const lower: number[] = []
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      upper.push(NaN)
      lower.push(NaN)
    } else {
      const slice = data.slice(i - period + 1, i + 1)
      const mean = sma[i]
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period
      const standardDeviation = Math.sqrt(variance)
      
      upper.push(mean + stdDev * standardDeviation)
      lower.push(mean - stdDev * standardDeviation)
    }
  }
  
  return {
    upper,
    middle: sma,
    lower
  }
}

// ==================== RSI相对强弱指数 ====================

/**
 * 计算RSI相对强弱指数
 * @param data 数据数组
 * @param period 周期，默认14
 * @returns RSI数组
 */
export const calculateRSI = (data: number[], period: number = 14): number[] => {
  const result: number[] = []
  const gains: number[] = []
  const losses: number[] = []
  
  // 计算价格变化
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1]
    gains.push(change > 0 ? change : 0)
    losses.push(change < 0 ? Math.abs(change) : 0)
  }
  
  result.push(NaN) // 第一个值无法计算
  
  for (let i = 0; i < gains.length; i++) {
    if (i < period - 1) {
      result.push(NaN)
    } else {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      
      if (avgLoss === 0) {
        result.push(100)
      } else {
        const rs = avgGain / avgLoss
        const rsi = 100 - (100 / (1 + rs))
        result.push(rsi)
      }
    }
  }
  
  return result
}

// ==================== MACD ====================

/**
 * 计算MACD
 * @param data 数据数组
 * @param fastPeriod 快线周期，默认12
 * @param slowPeriod 慢线周期，默认26
 * @param signalPeriod 信号线周期，默认9
 * @returns MACD数据 {macd, signal, histogram}
 */
export const calculateMACD = (
  data: number[], 
  fastPeriod: number = 12, 
  slowPeriod: number = 26, 
  signalPeriod: number = 9
): { macd: number[], signal: number[], histogram: number[] } => {
  const fastEMA = calculateEMA(data, fastPeriod)
  const slowEMA = calculateEMA(data, slowPeriod)
  
  // 计算MACD线
  const macd = fastEMA.map((fast, i) => {
    const slow = slowEMA[i]
    return isNaN(fast) || isNaN(slow) ? NaN : fast - slow
  })
  
  // 计算信号线
  const signal = calculateEMA(macd.filter(val => !isNaN(val)), signalPeriod)
  
  // 补齐信号线数组长度
  const signalPadded = [...Array(macd.length - signal.length).fill(NaN), ...signal]
  
  // 计算柱状图
  const histogram = macd.map((macdVal, i) => {
    const signalVal = signalPadded[i]
    return isNaN(macdVal) || isNaN(signalVal) ? NaN : macdVal - signalVal
  })
  
  return {
    macd,
    signal: signalPadded,
    histogram
  }
}

// ==================== KDJ随机指标 ====================

/**
 * 计算KDJ随机指标
 * @param candleData K线数据
 * @param period K值周期，默认9
 * @param smoothK K值平滑周期，默认3
 * @param smoothD D值平滑周期，默认3
 * @returns KDJ数据 {k, d, j}
 */
export const calculateKDJ = (
  candleData: CandlestickDataPoint[], 
  period: number = 9,
  smoothK: number = 3,
  smoothD: number = 3
): { k: number[], d: number[], j: number[] } => {
  const highs = extractHighPrices(candleData)
  const lows = extractLowPrices(candleData)
  const closes = extractClosePrices(candleData)
  
  const rsvs: number[] = []
  
  // 计算RSV
  for (let i = 0; i < candleData.length; i++) {
    if (i < period - 1) {
      rsvs.push(NaN)
    } else {
      const periodHighs = highs.slice(i - period + 1, i + 1)
      const periodLows = lows.slice(i - period + 1, i + 1)
      const maxHigh = Math.max(...periodHighs)
      const minLow = Math.min(...periodLows)
      
      if (maxHigh === minLow) {
        rsvs.push(50) // 避免除零
      } else {
        const rsv = ((closes[i] - minLow) / (maxHigh - minLow)) * 100
        rsvs.push(rsv)
      }
    }
  }
  
  // 计算K值（RSV的移动平均）
  const k = calculateSMA(rsvs, smoothK)
  
  // 计算D值（K值的移动平均）
  const d = calculateSMA(k, smoothD)
  
  // 计算J值
  const j = k.map((kVal, i) => {
    const dVal = d[i]
    return isNaN(kVal) || isNaN(dVal) ? NaN : 3 * kVal - 2 * dVal
  })
  
  return { k, d, j }
}

// ==================== 综合技术指标计算 ====================

/**
 * 计算所有技术指标
 * @param candleData K线数据
 * @param indicators 要计算的指标列表
 * @returns 技术指标数据
 */
export const calculateAllTechnicalIndicators = (
  candleData: CandlestickDataPoint[],
  indicators: string[] = ['MA5', 'MA20', 'BOLL', 'RSI']
): Record<string, number[]> => {
  const result: Record<string, number[]> = {}
  const closes = extractClosePrices(candleData)
  
  indicators.forEach(indicator => {
    switch (indicator) {
      case 'MA5':
        result.MA5 = calculateSMA(closes, 5)
        break
      case 'MA10':
        result.MA10 = calculateSMA(closes, 10)
        break
      case 'MA20':
        result.MA20 = calculateSMA(closes, 20)
        break
      case 'MA60':
        result.MA60 = calculateSMA(closes, 60)
        break
      case 'EMA12':
        result.EMA12 = calculateEMA(closes, 12)
        break
      case 'EMA26':
        result.EMA26 = calculateEMA(closes, 26)
        break
      case 'BOLL':
        const boll = calculateBollingerBands(closes, 20, 2)
        result.BOLL_UPPER = boll.upper
        result.BOLL_MIDDLE = boll.middle
        result.BOLL_LOWER = boll.lower
        break
      case 'RSI':
        result.RSI = calculateRSI(closes, 14)
        break
      case 'MACD':
        const macd = calculateMACD(closes, 12, 26, 9)
        result.MACD = macd.macd
        result.MACD_SIGNAL = macd.signal
        result.MACD_HISTOGRAM = macd.histogram
        break
      case 'KDJ':
        const kdj = calculateKDJ(candleData, 9, 3, 3)
        result.KDJ_K = kdj.k
        result.KDJ_D = kdj.d
        result.KDJ_J = kdj.j
        break
    }
  })
  
  return result
}
