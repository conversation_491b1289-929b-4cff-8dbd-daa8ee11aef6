/**
 * K线图工具函数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

import type { 
  CandlestickDataPoint, 
  CandlestickStatistics,
  DataTransformOptions,
  CandlestickTheme 
} from '@/types/candlestick'

// ==================== 数据验证 ====================

/**
 * 验证K线数据点
 * @param dataPoint K线数据点
 * @returns 是否有效
 */
export const isValidCandlestickData = (dataPoint: CandlestickDataPoint): boolean => {
  const { open, close, high, low, volume } = dataPoint
  
  // 基础数值验证
  if (isNaN(open) || isNaN(close) || isNaN(high) || isNaN(low) || isNaN(volume)) {
    return false
  }
  
  // 价格逻辑验证
  if (high < Math.max(open, close) || low > Math.min(open, close)) {
    return false
  }
  
  // 成交量不能为负
  if (volume < 0) {
    return false
  }
  
  return true
}

/**
 * 清理无效的K线数据
 * @param data K线数据数组
 * @returns 清理后的数据
 */
export const cleanCandlestickData = (data: CandlestickDataPoint[]): CandlestickDataPoint[] => {
  return data.filter(isValidCandlestickData)
}

// ==================== 数据转换 ====================

/**
 * 格式化日期字符串
 * @param dateStr 日期字符串
 * @param format 目标格式
 * @returns 格式化后的日期
 */
export const formatDateString = (dateStr: string, format: string = 'YYYY-MM-DD'): string => {
  const date = new Date(dateStr)
  
  if (isNaN(date.getTime())) {
    return dateStr // 如果无法解析，返回原字符串
  }
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'MM-DD':
      return `${month}-${day}`
    case 'YYYY-MM':
      return `${year}-${month}`
    case 'MM/DD':
      return `${month}/${day}`
    default:
      return `${year}-${month}-${day}`
  }
}

/**
 * 转换为ECharts K线数据格式
 * @param data K线数据数组
 * @returns ECharts格式的数据
 */
export const convertToEChartsFormat = (data: CandlestickDataPoint[]): any[] => {
  return data.map(item => [item.open, item.close, item.low, item.high])
}

/**
 * 转换为ECharts分类轴数据
 * @param data K线数据数组
 * @param dateFormat 日期格式
 * @returns 日期字符串数组
 */
export const convertToEChartsCategories = (
  data: CandlestickDataPoint[], 
  dateFormat: string = 'MM-DD'
): string[] => {
  return data.map(item => formatDateString(item.date, dateFormat))
}

/**
 * 转换为ECharts成交量数据
 * @param data K线数据数组
 * @returns 成交量数组
 */
export const convertToEChartsVolume = (data: CandlestickDataPoint[]): number[] => {
  return data.map(item => item.volume)
}

// ==================== 统计计算 ====================

/**
 * 计算K线统计信息
 * @param data K线数据数组
 * @returns 统计信息
 */
export const calculateCandlestickStatistics = (data: CandlestickDataPoint[]): CandlestickStatistics => {
  if (data.length === 0) {
    return {
      totalVolume: 0,
      avgVolume: 0,
      maxHigh: 0,
      minLow: 0,
      priceChange: 0,
      priceChangePercent: 0
    }
  }
  
  const totalVolume = data.reduce((sum, item) => sum + item.volume, 0)
  const avgVolume = totalVolume / data.length
  const maxHigh = Math.max(...data.map(item => item.high))
  const minLow = Math.min(...data.map(item => item.low))
  
  // 计算价格变化（最后一个与第一个的收盘价比较）
  const firstClose = data[0].close
  const lastClose = data[data.length - 1].close
  const priceChange = lastClose - firstClose
  const priceChangePercent = firstClose !== 0 ? (priceChange / firstClose) * 100 : 0
  
  return {
    totalVolume,
    avgVolume,
    maxHigh,
    minLow,
    priceChange,
    priceChangePercent
  }
}

/**
 * 计算涨跌统计
 * @param data K线数据数组
 * @returns 涨跌统计
 */
export const calculateRiseFallStatistics = (data: CandlestickDataPoint[]) => {
  let riseCount = 0
  let fallCount = 0
  let flatCount = 0
  
  data.forEach(item => {
    if (item.close > item.open) {
      riseCount++
    } else if (item.close < item.open) {
      fallCount++
    } else {
      flatCount++
    }
  })
  
  const total = data.length
  
  return {
    riseCount,
    fallCount,
    flatCount,
    riseRate: total > 0 ? (riseCount / total) * 100 : 0,
    fallRate: total > 0 ? (fallCount / total) * 100 : 0,
    flatRate: total > 0 ? (flatCount / total) * 100 : 0
  }
}

// ==================== 数据处理 ====================

/**
 * 按时间范围过滤数据
 * @param data K线数据数组
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 过滤后的数据
 */
export const filterDataByDateRange = (
  data: CandlestickDataPoint[],
  startDate: string,
  endDate: string
): CandlestickDataPoint[] => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  return data.filter(item => {
    const itemDate = new Date(item.date)
    return itemDate >= start && itemDate <= end
  })
}

/**
 * 数据降采样（用于大数据量优化）
 * @param data K线数据数组
 * @param maxPoints 最大数据点数
 * @returns 降采样后的数据
 */
export const downsampleData = (
  data: CandlestickDataPoint[],
  maxPoints: number
): CandlestickDataPoint[] => {
  if (data.length <= maxPoints) {
    return data
  }
  
  const step = Math.ceil(data.length / maxPoints)
  const result: CandlestickDataPoint[] = []
  
  for (let i = 0; i < data.length; i += step) {
    const chunk = data.slice(i, i + step)
    
    // 合并chunk为一个数据点
    const merged: CandlestickDataPoint = {
      date: chunk[0].date,
      open: chunk[0].open,
      close: chunk[chunk.length - 1].close,
      high: Math.max(...chunk.map(item => item.high)),
      low: Math.min(...chunk.map(item => item.low)),
      volume: chunk.reduce((sum, item) => sum + item.volume, 0)
    }
    
    result.push(merged)
  }
  
  return result
}

/**
 * 填充缺失的数据点
 * @param data K线数据数组
 * @param fillMethod 填充方法
 * @returns 填充后的数据
 */
export const fillMissingData = (
  data: CandlestickDataPoint[],
  fillMethod: 'forward' | 'backward' | 'interpolate' = 'forward'
): CandlestickDataPoint[] => {
  if (data.length === 0) return data
  
  const result = [...data]
  
  switch (fillMethod) {
    case 'forward':
      // 向前填充
      for (let i = 1; i < result.length; i++) {
        const current = result[i]
        const previous = result[i - 1]
        
        if (isNaN(current.open)) current.open = previous.close
        if (isNaN(current.close)) current.close = previous.close
        if (isNaN(current.high)) current.high = previous.close
        if (isNaN(current.low)) current.low = previous.close
        if (isNaN(current.volume)) current.volume = 0
      }
      break
      
    case 'backward':
      // 向后填充
      for (let i = result.length - 2; i >= 0; i--) {
        const current = result[i]
        const next = result[i + 1]
        
        if (isNaN(current.open)) current.open = next.open
        if (isNaN(current.close)) current.close = next.open
        if (isNaN(current.high)) current.high = next.open
        if (isNaN(current.low)) current.low = next.open
        if (isNaN(current.volume)) current.volume = 0
      }
      break
      
    case 'interpolate':
      // 线性插值（简化版本）
      for (let i = 1; i < result.length - 1; i++) {
        const current = result[i]
        const previous = result[i - 1]
        const next = result[i + 1]
        
        if (isNaN(current.close)) {
          current.close = (previous.close + next.close) / 2
          current.open = current.close
          current.high = current.close
          current.low = current.close
        }
        if (isNaN(current.volume)) current.volume = 0
      }
      break
  }
  
  return result
}

// ==================== 主题和样式 ====================

/**
 * 获取K线颜色
 * @param open 开盘价
 * @param close 收盘价
 * @param theme 主题配置
 * @returns 颜色值
 */
export const getCandleColor = (
  open: number, 
  close: number, 
  theme: CandlestickTheme
): string => {
  return close >= open ? theme.upColor : theme.downColor
}

/**
 * 生成渐变色配置
 * @param color 基础颜色
 * @param opacity 透明度
 * @returns 渐变色配置
 */
export const generateGradientColor = (color: string, opacity: number = 0.3) => {
  return {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      { offset: 0, color: color + Math.round(opacity * 255).toString(16).padStart(2, '0') },
      { offset: 1, color: color + '00' }
    ]
  }
}

// ==================== 格式化工具 ====================

/**
 * 格式化价格显示
 * @param price 价格
 * @param precision 精度
 * @param currency 货币符号
 * @returns 格式化后的价格字符串
 */
export const formatPrice = (
  price: number, 
  precision: number = 2, 
  currency: string = '¥'
): string => {
  if (isNaN(price)) return '--'
  return `${currency}${price.toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}

/**
 * 格式化成交量显示
 * @param volume 成交量
 * @returns 格式化后的成交量字符串
 */
export const formatVolume = (volume: number): string => {
  if (isNaN(volume)) return '--'
  
  if (volume >= 1000000) {
    return `${(volume / 1000000).toFixed(1)}M`
  } else if (volume >= 1000) {
    return `${(volume / 1000).toFixed(1)}K`
  } else {
    return volume.toString()
  }
}

/**
 * 格式化百分比显示
 * @param percent 百分比
 * @param precision 精度
 * @returns 格式化后的百分比字符串
 */
export const formatPercent = (percent: number, precision: number = 2): string => {
  if (isNaN(percent)) return '--'
  const sign = percent >= 0 ? '+' : ''
  return `${sign}${percent.toFixed(precision)}%`
}
