import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar as CalendarIcon,
  Clock,
  ChevronDown
} from 'lucide-react'
import { format, subDays, subWeeks, subMonths, startOfDay, endOfDay } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 时间范围选择器组件Props
 */
interface TimeRangeSelectorProps {
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 时间变化回调 */
  onTimeChange: (startTime: string, endTime: string) => void
  /** 样式类名 */
  className?: string
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 时间范围选择器组件
 * 提供快捷时间选择和自定义时间范围选择功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
export const TimeRangeSelector: React.FC<TimeRangeSelectorProps> = ({
  startTime,
  endTime,
  onTimeChange,
  className = '',
  disabled = false
}) => {
  const [isStartCalendarOpen, setIsStartCalendarOpen] = useState(false)
  const [isEndCalendarOpen, setIsEndCalendarOpen] = useState(false)

  // 快捷时间选项
  const quickTimeOptions = [
    {
      label: '最近7天',
      value: '7d',
      getRange: () => {
        const end = new Date()
        const start = subDays(end, 7)
        return { start, end }
      }
    },
    {
      label: '最近30天',
      value: '30d',
      getRange: () => {
        const end = new Date()
        const start = subDays(end, 30)
        return { start, end }
      }
    },
    {
      label: '最近3个月',
      value: '3m',
      getRange: () => {
        const end = new Date()
        const start = subMonths(end, 3)
        return { start, end }
      }
    },
    {
      label: '最近6个月',
      value: '6m',
      getRange: () => {
        const end = new Date()
        const start = subMonths(end, 6)
        return { start, end }
      }
    },
    {
      label: '最近1年',
      value: '1y',
      getRange: () => {
        const end = new Date()
        const start = subMonths(end, 12)
        return { start, end }
      }
    }
  ]

  // 格式化时间为显示格式
  const formatDisplayTime = (timeStr: string): string => {
    if (!timeStr) return ''
    try {
      const date = new Date(timeStr)
      return format(date, 'yyyy-MM-dd', { locale: zhCN })
    } catch {
      return timeStr
    }
  }

  // 格式化时间为API格式
  const formatApiTime = (date: Date): string => {
    return format(date, 'yyyy-MM-dd HH:mm:ss')
  }

  // 处理快捷时间选择
  const handleQuickTimeSelect = (option: typeof quickTimeOptions[0]) => {
    const { start, end } = option.getRange()
    const startTimeStr = formatApiTime(startOfDay(start))
    const endTimeStr = formatApiTime(endOfDay(end))
    onTimeChange(startTimeStr, endTimeStr)
  }

  // 处理开始时间选择
  const handleStartTimeSelect = (date: Date | undefined) => {
    if (!date) return
    
    const startTimeStr = formatApiTime(startOfDay(date))
    const currentEndTime = endTime || formatApiTime(endOfDay(new Date()))
    
    // 确保开始时间不晚于结束时间
    const endDate = new Date(currentEndTime)
    if (date > endDate) {
      const newEndTimeStr = formatApiTime(endOfDay(date))
      onTimeChange(startTimeStr, newEndTimeStr)
    } else {
      onTimeChange(startTimeStr, currentEndTime)
    }
    
    setIsStartCalendarOpen(false)
  }

  // 处理结束时间选择
  const handleEndTimeSelect = (date: Date | undefined) => {
    if (!date) return
    
    const endTimeStr = formatApiTime(endOfDay(date))
    const currentStartTime = startTime || formatApiTime(startOfDay(subDays(date, 7)))
    
    // 确保结束时间不早于开始时间
    const startDate = new Date(currentStartTime)
    if (date < startDate) {
      const newStartTimeStr = formatApiTime(startOfDay(date))
      onTimeChange(newStartTimeStr, endTimeStr)
    } else {
      onTimeChange(currentStartTime, endTimeStr)
    }
    
    setIsEndCalendarOpen(false)
  }

  // 获取当前选中的快捷选项
  const getCurrentQuickOption = () => {
    if (!startTime || !endTime) return null
    
    const start = new Date(startTime)
    const end = new Date(endTime)
    
    return quickTimeOptions.find(option => {
      const { start: optionStart, end: optionEnd } = option.getRange()
      const startDiff = Math.abs(start.getTime() - startOfDay(optionStart).getTime())
      const endDiff = Math.abs(end.getTime() - endOfDay(optionEnd).getTime())
      
      // 允许1天的误差
      return startDiff < 24 * 60 * 60 * 1000 && endDiff < 24 * 60 * 60 * 1000
    })
  }

  const currentQuickOption = getCurrentQuickOption()

  // 计算时间范围天数
  const getTimeRangeDays = (): number => {
    if (!startTime || !endTime) return 0
    
    try {
      const start = new Date(startTime)
      const end = new Date(endTime)
      return Math.ceil((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000))
    } catch {
      return 0
    }
  }

  const rangeDays = getTimeRangeDays()

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          时间范围
          {rangeDays > 0 && (
            <Badge variant="secondary" className="ml-2">
              {rangeDays}天
            </Badge>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 快捷时间选择 */}
        <div>
          <h4 className="text-sm font-medium mb-3">快捷选择</h4>
          <div className="flex flex-wrap gap-2">
            {quickTimeOptions.map(option => (
              <Button
                key={option.value}
                variant={currentQuickOption?.value === option.value ? "default" : "outline"}
                size="sm"
                onClick={() => handleQuickTimeSelect(option)}
                disabled={disabled}
                className="text-xs"
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>

        {/* 自定义时间选择 */}
        <div>
          <h4 className="text-sm font-medium mb-3">自定义范围</h4>
          <div className="grid grid-cols-2 gap-3">
            {/* 开始时间 */}
            <div>
              <label className="text-xs text-muted-foreground mb-1 block">
                开始时间
              </label>
              <Popover open={isStartCalendarOpen} onOpenChange={setIsStartCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                    disabled={disabled}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startTime ? formatDisplayTime(startTime) : '选择开始时间'}
                    <ChevronDown className="ml-auto h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startTime ? new Date(startTime) : undefined}
                    onSelect={handleStartTimeSelect}
                    disabled={(date) => 
                      date > new Date() || 
                      (endTime && date > new Date(endTime))
                    }
                    initialFocus
                    locale={zhCN}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* 结束时间 */}
            <div>
              <label className="text-xs text-muted-foreground mb-1 block">
                结束时间
              </label>
              <Popover open={isEndCalendarOpen} onOpenChange={setIsEndCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                    disabled={disabled}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endTime ? formatDisplayTime(endTime) : '选择结束时间'}
                    <ChevronDown className="ml-auto h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endTime ? new Date(endTime) : undefined}
                    onSelect={handleEndTimeSelect}
                    disabled={(date) => 
                      date > new Date() || 
                      (startTime && date < new Date(startTime))
                    }
                    initialFocus
                    locale={zhCN}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* 时间范围提示 */}
        {startTime && endTime && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">选择范围:</span>
              <span className="font-medium">
                {formatDisplayTime(startTime)} 至 {formatDisplayTime(endTime)}
              </span>
            </div>
            {rangeDays > 365 && (
              <div className="mt-2 text-xs text-amber-600">
                ⚠️ 时间范围超过1年，查询可能较慢
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
