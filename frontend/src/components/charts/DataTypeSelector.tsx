import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  DollarSign, 
  Percent,
  Info
} from 'lucide-react'

/**
 * 数据类型选择器组件Props
 */
interface DataTypeSelectorProps {
  /** 当前选中的数据类型 */
  selectedType: 'revenue' | 'profit' | 'commission'
  /** 数据类型变化回调 */
  onTypeChange: (type: 'revenue' | 'profit' | 'commission') => void
  /** 样式类名 */
  className?: string
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 数据类型选择器组件
 * 提供收入、利润、佣金三种数据类型的选择功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
export const DataTypeSelector: React.FC<DataTypeSelectorProps> = ({
  selectedType,
  onTypeChange,
  className = '',
  disabled = false
}) => {
  // 数据类型配置
  const dataTypes = [
    {
      id: 'revenue' as const,
      name: '收入数据',
      shortName: '收入',
      description: '基于充值订单的收入K线图',
      detail: '展示用户充值产生的收入变化趋势，反映平台的收入能力',
      icon: DollarSign,
      color: '#10b981',
      bgColor: '#10b98110',
      dataSource: 'vim_order_recharge',
      metrics: ['充值金额', '订单数量', '用户数量']
    },
    {
      id: 'profit' as const,
      name: '利润数据',
      shortName: '利润',
      description: '收入减去佣金支出的净利润K线图',
      detail: '展示扣除佣金成本后的实际盈利情况，反映平台的盈利能力',
      icon: TrendingUp,
      color: '#3b82f6',
      bgColor: '#3b82f610',
      dataSource: 'vim_order_recharge - sys_order',
      metrics: ['净利润', '利润率', '成本控制']
    },
    {
      id: 'commission' as const,
      name: '佣金数据',
      shortName: '佣金',
      description: '基于佣金结算订单的支出K线图',
      detail: '展示平台支付给主播的佣金变化趋势，反映平台的成本结构',
      icon: Percent,
      color: '#f59e0b',
      bgColor: '#f59e0b10',
      dataSource: 'sys_order',
      metrics: ['佣金支出', '结算订单', '主播数量']
    }
  ]

  // 获取当前选中的数据类型配置
  const selectedConfig = dataTypes.find(type => type.id === selectedType)

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          {selectedConfig && <selectedConfig.icon className="h-5 w-5" />}
          数据类型
          <Badge 
            variant="secondary"
            style={{ 
              backgroundColor: selectedConfig?.bgColor,
              color: selectedConfig?.color 
            }}
          >
            {selectedConfig?.shortName}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 数据类型选择 */}
        <div className="grid grid-cols-1 gap-3">
          {dataTypes.map(type => {
            const isSelected = type.id === selectedType
            const Icon = type.icon
            
            return (
              <div
                key={type.id}
                className={`p-4 rounded-lg border cursor-pointer transition-all ${
                  isSelected 
                    ? 'border-primary bg-primary/5 shadow-sm' 
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => !disabled && onTypeChange(type.id)}
              >
                <div className="flex items-start gap-3">
                  <div
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: type.bgColor }}
                  >
                    <Icon 
                      className="h-5 w-5" 
                      style={{ color: type.color }}
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">{type.name}</h4>
                      {isSelected && (
                        <Badge variant="default" className="text-xs">
                          当前选择
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-xs text-muted-foreground mb-2">
                      {type.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-1">
                      {type.metrics.map(metric => (
                        <Badge 
                          key={metric} 
                          variant="outline" 
                          className="text-xs"
                        >
                          {metric}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* 当前选择的详细信息 */}
        {selectedConfig && (
          <div className="p-4 bg-muted/50 rounded-lg">
            <div className="flex items-start gap-2 mb-2">
              <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div>
                <h5 className="font-medium text-sm mb-1">
                  {selectedConfig.name} - 详细说明
                </h5>
                <p className="text-xs text-muted-foreground mb-2">
                  {selectedConfig.detail}
                </p>
                <div className="text-xs text-muted-foreground">
                  <span className="font-medium">数据来源:</span> {selectedConfig.dataSource}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 数据类型对比说明 */}
        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
          <h5 className="font-medium text-sm mb-2 text-blue-900 dark:text-blue-100">
            💡 数据类型说明
          </h5>
          <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
            <li>• <strong>收入数据</strong>：反映平台的营收能力和用户活跃度</li>
            <li>• <strong>利润数据</strong>：反映平台的实际盈利能力和成本控制</li>
            <li>• <strong>佣金数据</strong>：反映平台的成本结构和主播激励情况</li>
          </ul>
        </div>

        {/* 快速切换按钮 */}
        <div className="flex gap-2">
          {dataTypes.map(type => (
            <Button
              key={type.id}
              variant={type.id === selectedType ? "default" : "outline"}
              size="sm"
              onClick={() => onTypeChange(type.id)}
              disabled={disabled}
              className="flex-1 text-xs"
            >
              <type.icon className="h-3 w-3 mr-1" />
              {type.shortName}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
