import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  TrendingUp, 
  Activity, 
  BarChart3,
  Settings2
} from 'lucide-react'
import type { TechnicalIndicatorsProps } from '@/types/candlestick'

/**
 * 技术指标选择和配置组件
 * 提供技术指标的开关控制和参数配置功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
export const TechnicalIndicators: React.FC<TechnicalIndicatorsProps> = ({
  enabled = false,
  selectedIndicators = [],
  onToggle,
  onIndicatorChange,
  className = ''
}) => {
  // 可用的技术指标配置
  const availableIndicators = [
    {
      id: 'MA5',
      name: 'MA5',
      fullName: '5日移动平均线',
      description: '短期趋势指标，反映近期价格走势',
      color: '#3b82f6',
      category: 'trend'
    },
    {
      id: 'MA10',
      name: 'MA10',
      fullName: '10日移动平均线',
      description: '中短期趋势指标',
      color: '#8b5cf6',
      category: 'trend'
    },
    {
      id: 'MA20',
      name: 'MA20',
      fullName: '20日移动平均线',
      description: '中期趋势指标，常用的支撑阻力线',
      color: '#f59e0b',
      category: 'trend'
    },
    {
      id: 'MA60',
      name: 'MA60',
      fullName: '60日移动平均线',
      description: '长期趋势指标，重要的支撑阻力位',
      color: '#ef4444',
      category: 'trend'
    },
    {
      id: 'EMA12',
      name: 'EMA12',
      fullName: '12日指数移动平均线',
      description: '对近期价格变化更敏感的趋势指标',
      color: '#06b6d4',
      category: 'trend'
    },
    {
      id: 'EMA26',
      name: 'EMA26',
      fullName: '26日指数移动平均线',
      description: '中期指数移动平均线',
      color: '#84cc16',
      category: 'trend'
    },
    {
      id: 'BOLL',
      name: 'BOLL',
      fullName: '布林带',
      description: '价格波动区间指标，包含上轨、中轨、下轨',
      color: '#6366f1',
      category: 'volatility'
    },
    {
      id: 'RSI',
      name: 'RSI',
      fullName: '相对强弱指数',
      description: '超买超卖指标，范围0-100',
      color: '#ec4899',
      category: 'momentum'
    }
  ]

  // 按类别分组指标
  const indicatorsByCategory = {
    trend: availableIndicators.filter(indicator => indicator.category === 'trend'),
    volatility: availableIndicators.filter(indicator => indicator.category === 'volatility'),
    momentum: availableIndicators.filter(indicator => indicator.category === 'momentum')
  }

  const categoryLabels = {
    trend: '趋势指标',
    volatility: '波动性指标',
    momentum: '动量指标'
  }

  const categoryIcons = {
    trend: TrendingUp,
    volatility: Activity,
    momentum: BarChart3
  }

  // 处理指标选择变化
  const handleIndicatorToggle = (indicatorId: string) => {
    const newSelected = selectedIndicators.includes(indicatorId)
      ? selectedIndicators.filter(id => id !== indicatorId)
      : [...selectedIndicators, indicatorId]
    
    onIndicatorChange?.(newSelected)
  }

  // 处理全选/取消全选
  const handleSelectAll = (category: keyof typeof indicatorsByCategory) => {
    const categoryIndicators = indicatorsByCategory[category].map(indicator => indicator.id)
    const allSelected = categoryIndicators.every(id => selectedIndicators.includes(id))
    
    if (allSelected) {
      // 取消选择该类别的所有指标
      const newSelected = selectedIndicators.filter(id => !categoryIndicators.includes(id))
      onIndicatorChange?.(newSelected)
    } else {
      // 选择该类别的所有指标
      const newSelected = [...new Set([...selectedIndicators, ...categoryIndicators])]
      onIndicatorChange?.(newSelected)
    }
  }

  // 清除所有选择
  const handleClearAll = () => {
    onIndicatorChange?.([])
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Settings2 className="h-5 w-5" />
            技术指标
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">启用技术指标</span>
            <Switch
              checked={enabled}
              onCheckedChange={onToggle}
            />
          </div>
        </div>
        
        {enabled && selectedIndicators.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">已选择:</span>
            <div className="flex flex-wrap gap-1">
              {selectedIndicators.map(indicatorId => {
                const indicator = availableIndicators.find(ind => ind.id === indicatorId)
                return indicator ? (
                  <Badge 
                    key={indicatorId} 
                    variant="secondary"
                    style={{ backgroundColor: `${indicator.color}20`, color: indicator.color }}
                  >
                    {indicator.name}
                  </Badge>
                ) : null
              })}
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleClearAll}
              className="text-xs"
            >
              清除
            </Button>
          </div>
        )}
      </CardHeader>

      {enabled && (
        <CardContent className="space-y-6">
          {Object.entries(indicatorsByCategory).map(([category, indicators]) => {
            const CategoryIcon = categoryIcons[category as keyof typeof categoryIcons]
            const allSelected = indicators.every(indicator => 
              selectedIndicators.includes(indicator.id)
            )
            return (
              <div key={category} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CategoryIcon className="h-4 w-4 text-muted-foreground" />
                    <h4 className="font-medium text-sm">
                      {categoryLabels[category as keyof typeof categoryLabels]}
                    </h4>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSelectAll(category as keyof typeof indicatorsByCategory)}
                    className="text-xs"
                  >
                    {allSelected ? '取消全选' : '全选'}
                  </Button>
                </div>

                <div className="grid grid-cols-1 gap-3">
                  {indicators.map(indicator => {
                    const isSelected = selectedIndicators.includes(indicator.id)
                    
                    return (
                      <div
                        key={indicator.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-all ${
                          isSelected 
                            ? 'border-primary bg-primary/5' 
                            : 'border-border hover:border-primary/50'
                        }`}
                        onClick={() => handleIndicatorToggle(indicator.id)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: indicator.color }}
                              />
                              <span className="font-medium text-sm">
                                {indicator.fullName}
                              </span>
                              <Badge variant="outline" className="text-xs">
                                {indicator.name}
                              </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {indicator.description}
                            </p>
                          </div>
                          
                          <Switch
                            checked={isSelected}
                            onCheckedChange={() => handleIndicatorToggle(indicator.id)}
                          />
                        </div>
                      </div>
                    )
                  })}
                </div>

                {category !== 'momentum' && <Separator />}
              </div>
            )
          })}

          {selectedIndicators.length > 0 && (
            <div className="mt-6 p-4 bg-muted/50 rounded-lg">
              <h5 className="font-medium text-sm mb-2">使用说明</h5>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 趋势指标：帮助识别价格趋势方向和强度</li>
                <li>• 波动性指标：显示价格波动范围和支撑阻力位</li>
                <li>• 动量指标：判断价格变化的速度和超买超卖状态</li>
                <li>• 建议同时使用不同类型的指标进行综合分析</li>
              </ul>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}
